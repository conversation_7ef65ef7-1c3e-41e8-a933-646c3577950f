# dspy 中的结构化输出工作机制

## 1. 总体流程概览
- DSPy 用 `Signature` 来声明输入/输出字段、类型与任务指令，这些声明驱动后续的提示格式化与结果解析（`signatures/signature.py:34-189`）。
- 运行时，`Predict` 等模块会选取当前配置的 `adapter`（默认 `ChatAdapter`），将结构化的输入映射为提示消息，并在返回后解析为强类型结果（`predict/predict.py:185-208`）。
- `Adapter` 基类统一处理调用：预处理签名、构造消息、调用 LLM，然后基于签名信息解析响应并补齐缺失字段（`adapters/base.py:32-140`）。
- 不同适配器提供不同的结构化约束方式：`ChatAdapter` 通过锚点标记文本结构，`JSONAdapter` 通过 OpenAI Structured Outputs/JSON Schema，`XMLAdapter` 则使用 XML 标签。还可以串联辅助解析的 `TwoStepAdapter` 等高级策略。

## 2. 通过 Signature 声明结构
- `SignatureMeta` 在构造派生签名类时，会确保所有字段都使用 `InputField`/`OutputField` 声明，并记录字段顺序与描述（`signatures/signature.py:137-189`）。
- `Signature.instructions` 为适配器提供任务说明文本，缺省时会根据输入/输出字段自动生成“给定… 产出…”的描述（`signatures/signature.py:34-38`）。
- 字段的 `annotation`、`default`、`json_schema_extra`（例如 `desc`、`prefix`）等元数据被适配器用于提示模板化和结果校验。

## 3. Adapter 基类的结构化管线
- `_call_preprocess` 会根据签名自动处理特殊字段：若启用 `use_native_function_calling` 且存在 `ToolCalls` 字段，将工具信息转为 LiteLLM 所需格式，并从签名中临时移除相关字段以便 LLM 原生函数调用（`adapters/base.py:32-71`）。
- `format` 负责将签名、few-shot 样例、会话历史与当前输入整理成消息列表，系统提示中包含字段描述、结构约束和任务目标，保证模型从提示层面理解所需的输出结构（`adapters/base.py:156-200`）。
- `_call_postprocess` 接收来自 `LM` 的原始响应（可能是纯文本或包含 `tool_calls` 等元信息的字典），调用 `parse` 将文本解析为字典，并补齐未出现在响应中的输出字段（以 `None` 占位），同时处理工具调用与引用信息（`adapters/base.py:73-126`）。

## 4. ChatAdapter：通过锚点提示实现结构化文本
- `ChatAdapter` 在系统提示中声明输入输出的分段结构，并引导模型使用 `[[ ## field_name ## ]]` 标记每个字段，末尾追加 `[[ ## completed ## ]]` 结束标记（`adapters/chat_adapter.py:75-95`）。
- 构造用户消息时会逐个字段插入前述标记，同时对输入值进行格式化（`adapters/chat_adapter.py:102-123`）。输出解析阶段按行拆分，查找标记头来定位字段内容，然后调用通用的 `parse_value` 将字符串转换为声明的 Python 类型（`adapters/chat_adapter.py:169-199` 与 `adapters/utils.py:137-185`）。
- 若 `ChatAdapter` 解析失败（例如模型未严格遵守格式），会自动回退到 `JSONAdapter` 重试，从而提高结构化解析的鲁棒性（`adapters/chat_adapter.py:20-67`）。

## 5. JSONAdapter：利用 OpenAI Structured Outputs/JSON Schema
- `JSONAdapter` 继承自 `ChatAdapter`，在调用前探测模型是否支持 `response_format` 参数；支持时优先构造 Structured Output 模式，不支持则退回普通 JSON 模式（`adapters/json_adapter.py:46-82`）。
- `_get_structured_outputs_response_format` 根据签名的输出字段动态构造一个 Pydantic 模型，设置 `extra="forbid"` 并递归地为所有对象属性补齐 `required` 和 `additionalProperties=False`，确保生成的 JSON Schema 满足 OpenAI Structured Outputs 的严格要求（`adapters/json_adapter.py:210-288`）。
- 为了兼容工具调用与映射类型，适配器在生成结构时跳过 `ToolCalls` 字段（交由原生函数调用返回），并检测 `dict[str, Any]` 等开放式映射，遇到此类字段则抛出异常提示无法使用 Structured Outputs（`adapters/json_adapter.py:54-57; 231-237`）。
- 在提示层面，`JSONAdapter` 会强调“输出必须是 JSON 对象”并给出字段顺序与类型提示，帮助模型理解预期 schema（`adapters/json_adapter.py:105-135`）。
- 解析阶段先用正则截取第一个 JSON 对象，再用 `json_repair` 自动纠正潜在的语法缺陷，最终仅保留签名中声明的字段并依类型转换（`adapters/json_adapter.py:149-179`）。

## 6. 类型安全与语义校准
- `parse_value` 支持 `Literal`、`Enum`、嵌套 Pydantic 模型、Union 等常见类型，通过 `pydantic.TypeAdapter` 执行转换，同时对字符串输入尝试 `json_repair` / `ast.literal_eval` 以提高容错性（`adapters/utils.py:137-185`）。
- `format_field_value` 根据字段类型决定如何在提示中呈现输入值，例如列表编号、JSON 序列化、或直接字符串，避免 LLM 因输入格式不一致而偏离结构（`adapters/utils.py:36-65`）。
- `Adapter._call_postprocess` 在解析后还会处理 `tool_calls` 和 `citations`，对工具调用参数再次执行 JSON 解析并封装成 `ToolCalls` 对象，保障二次消费时结构一致（`adapters/base.py:107-119`）。

## 7. 扩展适配器与高级策略
- `BAMLAdapter` 在系统提示中为复杂的 Pydantic 嵌套模型生成紧凑易读的 schema（含注释），提升模型对深层结构的遵循度，适合医疗、法律等字段含义丰富的场景（`adapters/baml_adapter.py:1-165`）。
- `XMLAdapter` 通过 XML 标签约束输出格式，解析时按标签提取内容并复用 `parse_value` 完成类型转换，适配需要嵌套节点的工作流（`adapters/xml_adapter.py:1-49`）。
- `TwoStepAdapter` 将“生成”与“结构化提取”拆成两个模型：第一步主模型自由生成，第二步使用较小的模型和 `ChatAdapter` 对文本进行结构化抽取，在推理模型不擅长遵循格式时尤其有用（`adapters/two_step_adapter.py:1-126`）。

## 8. 与 LM 的集成细节
- `Predict.forward` 会从 `dspy.settings` 读取全局配置的适配器与 LM，以统一入口驱动结构化调用（`predict/predict.py:181-208`）。
- `LM.forward` 基于 LiteLLM 将 `messages`、`response_format`、`tools` 等参数原样传给底层模型；Structured Outputs/JSON 模式本质上就是给 LiteLLM/OpenAI 的请求附加 `response_format`（`clients/lm.py:62-123`）。
- `BaseLM._process_completion` 将 OpenAI/LiteLLM 的返回结构规整为 `text` + 元数据，供适配器解析；这层不会自行做结构化解析，全部责任交给适配器，从而保持职责分离（`clients/base_lm.py:60-139`）。

## 9. 综合工作流
1. 用户定义 `Signature` 并选择适配器，例如 `dspy.configure(adapter=dspy.JSONAdapter())`。
2. `Predict` 等组件在调用时构造含字段描述的系统提示、few-shot 样例与用户消息。
3. 适配器根据配置选择 Structured Output / JSON / 标记式模板，将 `response_format`、`tools` 等信息同步给 LLM。
4. LLM 返回后，适配器按签名字段解析文本/JSON，借助 `parse_value` 恢复为声明类型；若解析失败，`ChatAdapter` 会回退到 `JSONAdapter` 再试。
5. `Prediction.from_completions` 将解析结果包装成 `Prediction` 对象，字段访问即得到结构化产物，后续优化或评估模块可以直接处理强类型结果。

通过上述设计，DSPy 在“提示约束 + 模型原生 Structured Outputs + 解析校验”之间形成闭环，无论模型是否原生支持结构化输出，都能在不同适配器之间找到兼容方案，实现稳定的结构化结果生产。
