**适用范围**
- 基于当前仓库（最新 dspy）的 `adapters/` 实现与在 `predict/` 的调用路径进行总结。
- 回答：Adapter 有哪些种类、工作机制、如何切换、场景如何选择，并给出代码参照与示例。

**Adapter 种类**
- ChatAdapter: 面向“分段标记”的对话格式，使用字段标记 `[[ ## name ## ]]`，默认适配大多数聊天模型。参见 `adapters/chat_adapter.py:28`。
- JSONAdapter: 在支持的提供商上优先启用 Structured Outputs/JSON 模式，并默认开启原生 Function Calling 集成；失败时回退到 JSON。参见 `adapters/json_adapter.py:41`。
- XMLAdapter: 用 XML 标签包裹字段，适合对 JSON 不敏感或偏好标签式提取的模型。参见 `adapters/xml_adapter.py:10`。
- TwoStepAdapter: 两阶段适配器。阶段1用主模型自由输出；阶段2调用一个“小模型＋ChatAdapter”从文本中抽取结构化结果，适合推理模型（o系列）难以稳定产出结构化时。参见 `adapters/two_step_adapter.py:21`。
- BAMLAdapter: 扩展自 JSONAdapter，对 Pydantic 嵌套结构的指引更友好（受 BAML 启发），给出紧凑、易遵循的类型说明。参见 `adapters/baml_adapter.py:133`。
- Adapter(Base): 抽象基类，定义了统一的调用流程与扩展点（format/parse 等）。参见 `adapters/base.py:20`。

**工作原理（统一调用链）**
- 入口：`Predict` 等模块在前向时取 `settings.adapter or ChatAdapter()`，调用其 `__call__`/`acall`。参见 `predict/predict.py:185` 与 `predict/predict.py:199`。
- 统一流程（Adapter 基类）：
  - 预处理 `_call_preprocess`：
    - 如果启用原生 Function Calling（`use_native_function_calling=True`）且签名包含工具输入/输出（输入 `list[dspy.Tool]`/`dspy.Tool`，输出 `dspy.ToolCalls`），则通过 `litellm` 注入 `tools` 参数，并在实际调用时临时从签名里剔除这些字段。参见 `adapters/base.py:32` 与 `adapters/base.py:39-66`。
    - 如果输出中有 `dspy.Citations` 字段，会在发送前暂时从签名移除，解析后再挂回。参见 `adapters/base.py:67-71`、`adapters/base.py:81-83`。
  - 格式化 `format`：
    - 生成 system 提示：字段说明、字段结构、任务说明三段合并。见 `adapters/base.py:215-221`。
    - 拼接 few-shot demos 与历史（输入里若含 `dspy.History`，会格式化历史再移除该输入字段）。见 `adapters/base.py:203-214`、`adapters/base.py:223-231`、`adapters/types/history.py:1`。
    - 将自定义 Type（如图像/音频等）切分为 content blocks。见 `adapters/types/base_type.py` 与 `adapters/types/base_type.py` 中的 `split_message_content_for_custom_types` 调用 `adapters/base.py:233`。
  - 调用底层 LM：传入 messages 与 `lm_kwargs`。
  - 解析 `_call_postprocess`：将 LM 输出文本交给 `parse`，并把 `tool_calls`/`citations`/`logprobs` 等附着回结果。参见 `adapters/base.py:73-107`。

**各适配器的要点**
- ChatAdapter（默认回退器）：
  - 输入/输出用 `[[ ## field ## ]]` 分段；输出末尾要求 `[[ ## completed ## ]]`。见 `adapters/chat_adapter.py:75-95`。
  - 解析：按分段标记切块并逐字段做类型转换；缺字段抛 `AdapterParseError`。见 `adapters/chat_adapter.py:169-204`。
  - 容错：若报错（非上下文超限），自动回退到 JSONAdapter 再试一遍。见 `adapters/chat_adapter.py:37-47` 与 `adapters/chat_adapter.py:57-67`。

- JSONAdapter（结构化优先）：
  - 默认 `use_native_function_calling=True`，在支持的提供商上优先启用 `response_format`（Structured Outputs）；失败时回退到 `{"type": "json_object"}`。见 `adapters/json_adapter.py:41-45`、`adapters/json_adapter.py:69-82`、`adapters/json_adapter.py:84-103`。
  - 若输出含开放映射（如 `dict[str, Any]`）或禁用原生函数调用且包含工具调用输出，会强制走 JSON 模式。见 `adapters/json_adapter.py:28-38`、`adapters/json_adapter.py:54-59`。
  - 解析：从模型输出中抽出最外层 JSON，对各字段按签名注解做强校验转换。见 `adapters/json_adapter.py:186-207`。

- XMLAdapter（标签式）：
  - `format` 以 `<name>...</name>` 包裹字段；`parse` 用正则抓取各字段并转型。见 `adapters/xml_adapter.py:10-26`、`adapters/xml_adapter.py:28-47`。

- TwoStepAdapter（两阶段抽取）：
  - 阶段1：对主模型仅给出任务与输入（不强制结构）；
  - 阶段2：将主模型文本输出喂给“小模型＋ChatAdapter”的抽取签名（`text -> 原签名输出字段`）再解析。见 `adapters/two_step_adapter.py:48-75`、`adapters/two_step_adapter.py:77-105`、`adapters/two_step_adapter.py:206-229`。

- BAMLAdapter（Pydantic 友好）：
  - 针对复杂嵌套 Pydantic 输出，生成紧凑、可读性高的类型提示（含注释），提升中小模型对结构要求的遵循度。见 `adapters/baml_adapter.py:182-231`。

**如何在代码中切换 Adapter**
- 全局设置（影响之后的所有模块调用）：
  - `dspy.configure(adapter=dspy.ChatAdapter())`
  - `dspy.configure(adapter=dspy.JSONAdapter())`
  - `dspy.configure(adapter=dspy.XMLAdapter())`
  - `dspy.configure(adapter=dspy.TwoStepAdapter(dspy.LM("openai/gpt-4o-mini")))`
  - `dspy.configure(adapter=dspy.BAMLAdapter())`
  - 配置机制参见 `dsp/utils/settings.py:9-31`、`dsp/utils/settings.py:158-165`。

- 作用域内临时切换（仅当前上下文线程内生效）：
  - `with dspy.context(adapter=dspy.JSONAdapter()): program(...)` 参见 `dsp/utils/settings.py:166-181`。

- 模块内（进阶）包装：
  - 例如 `Refine` 里动态派生一个 `WrapperAdapter` 给模块注入额外提示字段，再委托真实适配器执行。见 `predict/refine.py:104-135`。

- 与工具调用（Function Calling）的集成：
  - 在签名中声明：输入 `tools: list[dspy.Tool]` 或 `dspy.Tool`，输出 `calls: dspy.ToolCalls`；
  - 使用 `JSONAdapter()`（默认启用原生 FC）或 `ChatAdapter(use_native_function_calling=True)`；
  - 若提供商与模型支持，底层会通过 `litellm` 注入 `tools`，并在解析阶段把函数名与参数还原到 `ToolCalls`。见 `adapters/base.py:39-66`、`adapters/base.py:107-116`、`adapters/types/tool.py`。

- 与对话历史的集成：
  - 在签名中声明 `history: dspy.History = dspy.InputField()`；传入 `dspy.History(messages=[{...}])` 即可按适配器格式插入用户/助手多轮记录。见 `adapters/types/history.py:1`、`adapters/base.py:203-231`。

**简要示例**
- 全局使用 JSONAdapter（严格结构化）：
  - `dspy.configure(lm=dspy.LM("openai/gpt-4o-mini"), adapter=dspy.JSONAdapter())`
  - `predict = dspy.Predict("question -> answer")`
  - `predict(question="...")`

- 临时切换到 TwoStepAdapter（主模型为推理模型 o3，抽取模型用 gpt-4o-mini）：
  - `adapter = dspy.TwoStepAdapter(dspy.LM("openai/gpt-4o-mini"))`
  - `with dspy.context(adapter=adapter): dspy.Predict("q->a")(q="...")`

- 工具调用（原生 Function Calling）：
  - 签名含 `tools: list[dspy.Tool]` 与 `calls: dspy.ToolCalls`，配置 `dspy.JSONAdapter()`；
  - 运行后 `calls` 中包含 `[{name, args}]`，可据此执行工具并继续流程。

**如何选择（场景建议）**
- 先选 ChatAdapter：
  - 面向通用问答/写作/多段说明等任务；输出字段简单、可读性优先；模型对分段标记遵循度较高。
- 需要强结构与类型校验：
  - 选 JSONAdapter。优先尝试 Structured Outputs；对于 Pydantic/枚举/嵌套等类型解析更稳健；还原失败会抛错，方便定位。
- 复杂/嵌套的 Pydantic 输出：
  - 选 BAMLAdapter，给模型更友好的结构提示，尤其对中小模型遵循度提升明显。
- 模型对 JSON 不稳定或语料偏向标签：
  - 选 XMLAdapter（少量场景下更服从标签结构）。
- 主模型为“推理类/CoT 强”的大模型（如 o 系列），但结构化输出不稳定：
  - 选 TwoStepAdapter：主模型专注推理，小模型专注抽取与结构化。
- 工具调用（Function Calling）：
  - 优先 JSONAdapter（默认启用）；或 ChatAdapter(use_native_function_calling=True)。若提供商不支持，则会回退到纯文本/JSON 解析路径。

**常见提示**
- 提供商能力差异：Structured Outputs/Function Calling 是否可用取决于模型与提供商；JSONAdapter 会自动回退。
- 签名要“类型化”：输出字段尽量给出精确注解（枚举、Pydantic、列表/字典具体元素类型等），有助于模型服从与解析成功率。
- 上下文超限：ChatAdapter 超限不会自动换适配器（直接抛错）；其他异常会尝试回退到 JSONAdapter 再试一次。
- 开放映射 `dict[str, Any]`：Structured Outputs 不支持，JSONAdapter 会转用简单 JSON 模式。
