# dspy 优化器总览（Teleprompters/Optimizers）

本文基于当前仓库源码（teleprompt 目录）与 dspy 常识，系统梳理 dspy 中的所有优化器（Teleprompter）。对每个优化器给出：设计思路、适用场景、物料准备（需准备的数据/模型/度量等）与最小 Demo。示例代码均为最小可读示例，实际运行需按你本地可用的 LLM/适配器进行调整。

准备共用示例所需的最小环境：（仅示意）
- 配置 LM：`dspy.settings.configure(lm=your_lm, experimental=True)`（如 OpenAI、Databricks、或本地 LM）
- 一个简单任务签名与模块：
  - 签名：`class QA(dspy.Signature): question -> answer`
  - 模块：`qa = dspy.ChainOfThought(QA)` 或 `dspy.Predict(QA)`
- 训练集：`trainset = [dspy.Example(question=..., answer=...).with_inputs("question"), ...]`
- 简单指标：`def em(gold, pred, trace=None): return int(gold.answer.strip().lower()==pred.answer.strip().lower())`


## 目录（按模块导出顺序 & 常用度）
- LabeledFewShot（有标签少样本）
- BootstrapFewShot（自举 few-shot）
- BootstrapFewShotWithRandomSearch（自举+随机搜索）
- BootstrapFewShotWithOptuna（自举+Optuna）
- BootstrapFinetune（自举+权重微调）
- MIPROv2（多候选联合搜索与小批评估）
- COPRO（基于候选与评分的指令/前缀搜索）
- InferRules（从示例诱导规则并注入）
- SIMBA（随机-自省-小批上升）
- GEPA（带反馈的反思式程序优化）
- KNNFewShot（检索式 few-shot）
- Ensemble（集合策略）
- BetterTogether（提示+权重交替优化）
- AvatarOptimizer（面向 Avatar 工程流的指令改进）
- GRPO（群相对偏好强化学习，实验性，未在 teleprompt.__all__ 暴露）


---

## LabeledFewShot（teleprompt/vanilla.py: LabeledFewShot）
- 设计思路
  - 直接从人工标注的训练集挑选 k 个示例，作为每个预测器的 few-shot demos 放入 prompt。
- 适用场景
  - 任务简单、示例可靠；希望拥有稳定的“标注示例提示”基线。
- 物料准备
  - 训练集：`list[dspy.Example]`，并能 `with_inputs(...)` 指定输入字段。
  - LM：任意可用 LM。
- 最小 Demo
  ```python
  import dspy

  class QA(dspy.Signature):
      """Answer the question"""
      question = dspy.InputField()
      answer = dspy.OutputField()

  dspy.settings.configure(lm=your_lm)
  qa = dspy.ChainOfThought(QA)
  trainset = [
      dspy.Example(question="Capital of France?", answer="Paris").with_inputs("question"),
      dspy.Example(question="2+2?", answer="4").with_inputs("question"),
  ]

  tele = dspy.LabeledFewShot(k=2)
  compiled = tele.compile(qa, trainset=trainset)
  print(compiled(question="Capital of Belgium?").answer)
  ```


## BootstrapFewShot（teleprompt/bootstrap.py: BootstrapFewShot）
- 设计思路
  - 用“教师程序”（可默认等同学生的深拷贝）在训练集上执行，捕获 trace，将每步 I/O 变成增强的 few-shot demos；结合少量真标注示例，训练出更强的 few-shot 提示。
  - 支持 metric/threshold 过滤，支持多轮 rollouts 以绕过缓存、获取多样 trace。
- 适用场景
  - 有可运行的学生/教师程序，能在训练集上给出较为正确的轨迹输出；希望自动收集高质量示例作为提示。
- 物料准备
  - 训练集 `trainset`；可选教师程序 `teacher`；可选 `metric(example, prediction, trace)` 过滤成功案例。
  - 参数：`max_bootstrapped_demos`、`max_labeled_demos`、`max_rounds` 等。
- 最小 Demo
  ```python
  tele = dspy.BootstrapFewShot(metric=em, max_bootstrapped_demos=4, max_labeled_demos=4)
  compiled = tele.compile(qa, trainset=trainset)  # teacher 缺省为 student.deepcopy()
  ```


## BootstrapFewShotWithRandomSearch（teleprompt/random_search.py）
- 设计思路
  - 在“自举 few-shot”的基础上随机打乱/采样不同 demo 数量与顺序，生成多套候选程序；用验证集打分，择优输出，并保留候选列表。
- 适用场景
  - few-shot 效果对示例组合/顺序敏感；希望快速探索多候选并选最优。
- 物料准备
  - `metric`、`trainset`（可拆分/指定 `valset`），可选 `teacher_settings`。
  - 超参：候选数量 `num_candidate_programs`、线程数、停止阈值等。
- 最小 Demo
  ```python
  rs = dspy.BootstrapFewShotWithRandomSearch(metric=em, num_candidate_programs=8)
  best = rs.compile(qa, trainset=trainset, valset=trainset)
  # best.candidate_programs 保留所有候选与分数
  ```


## BootstrapFewShotWithOptuna（teleprompt/teleprompt_optuna.py）
- 设计思路
  - 先用 BootstrapFewShot 生成一批 demos，再用 Optuna 选择每个预测器的 demo 组合以最大化验证集得分。
- 适用场景
  - 需要系统性搜索 demo 组合；已安装 Optuna，并允许多次评估。
- 物料准备
  - `metric`、`trainset/valset`、可选 `teacher`；需安装 `optuna`。
- 最小 Demo
  ```python
  opt = dspy.BootstrapFewShotWithOptuna(metric=em, num_candidate_programs=16)
  best = opt.compile(qa, trainset=trainset, valset=trainset, max_demos=3)
  ```


## BootstrapFinetune（teleprompt/bootstrap_finetune.py）
- 设计思路
  - 先用 `bootstrap_trace_data` 从（学生或教师）程序产生“训练对话/调用数据”，再按 LM Provider 的适配器格式整理并启动 LM 的原生 finetune；可多预测器多任务。
- 适用场景
  - 后端 LM 支持微调（如 OpenAI/Databricks/本地 LoRA 等）；希望权重层面学习而非仅提示工程。
- 物料准备
  - 学生/教师程序，且“所有预测器必须有 lm”（`all_predictors_have_lms`）。
  - `metric`（可用于筛选 trace 数据）、`adapter`（默认 ChatAdapter）、`train_kwargs`（按 LM 要求）。
  - 可能的多线程限制，微调任务数量与线程数需匹配。
- 最小 Demo（伪示例：需可微调的 LM）
  ```python
  ft = dspy.BootstrapFinetune(metric=em, multitask=True, train_kwargs={})
  # 确保每个 predictor 都已设置 lm： qa.set_lm(your_finetunable_lm)
  compiled = ft.compile(qa, trainset=trainset)  # 可传 teacher=[...] 做多教师集数据自举
  ```


## MIPROv2（teleprompt/mipro_optimizer_v2.py）
- 设计思路
  - 统一搜索“指令候选 + few-shot 候选”并在验证集上用 Evaluate 评价；支持自动预算档位（light/medium/heavy）、minibatch 评估、Optuna 超参试验、以及提示模型与任务模型分离统计。
  - 步骤：自举 few-shot → 生成若干指令候选（可用 GroundedProposer/数据感知）→ 组合搜索并在小批/全量上评估 → 输出最优程序并可保存日志。
- 适用场景
  - 综合提示工程优化；希望在预算可控前提下获得“指令×示例”的较优组合。
- 物料准备
  - `metric` 必填；可选 `prompt_model`（用于生成候选）、`task_model`（用于评估），`trainset/valset`；可选 `auto='light|medium|heavy'`。
  - 线程数、错误上限、日志目录、随机种子等。
- 最小 Demo
  ```python
  mipro = dspy.MIPROv2(metric=em, auto="light")
  best = mipro.compile(qa, trainset=trainset)  # 不传 valset 时，内部按 80/20 切分
  ```


## COPRO（teleprompt/copro_optimizer.py）
- 设计思路
  - 以“生成候选指令+输出前缀（prompt prefix）→ 在验证集上评分→ 迭代生成新候选”的方式进行多轮 breadth×depth 搜索；可选独立 prompt_model；可跟踪统计。
- 适用场景
  - 主要优化“指令与输出前缀”，没有/不依赖 few-shot；需要多批生成与逐步改良。
- 物料准备
  - `metric`、`trainset/valset`、可选 `prompt_model`、超参：`breadth/depth/init_temperature`。
- 最小 Demo
  ```python
  copro = dspy.COPRO(metric=em, breadth=6, depth=2)
  best = copro.compile(qa, trainset=trainset, eval_kwargs=dict(display_progress=True))
  ```


## InferRules（teleprompt/infer_rules.py）
- 设计思路
  - 基于一组 demos，调用“规则诱导”子程序生成自然语言规则，将其附加到每个预测器的 instructions 中；循环若干候选，验证集上评分择优。
- 适用场景
  - 任务需要抽象可迁移的“操作规则”；few-shot 本身不够稳健时，试图给 LLM 更明确的规则指引。
- 物料准备
  - `trainset/valset`（不传则 50/50 切分）、`metric`，可选 `num_candidates/num_rules`。
- 最小 Demo
  ```python
  ir = dspy.InferRules(metric=em, num_candidates=5, num_rules=6)
  best = ir.compile(qa, trainset=trainset)
  ```


## SIMBA（teleprompt/simba.py）
- 设计思路
  - Stochastic Introspective Mini-Batch Ascent：按小批（bsize）对候选程序进行“轨迹采样→评估→从梯度上升方向生成候选”，策略包含“追加 demo”与“生成改进规则”；多轮迭代，最终在全量 trainset 上打分选优。
- 适用场景
  - 希望模型“自省”并不断总结成功/失败差异以改进；在预算内的迭代式、启发式优化。
- 物料准备
  - `metric(gold, pred) -> float`、`trainset`、可选线程数；超参：`bsize/num_candidates/max_steps/max_demos` 等。
- 最小 Demo
  ```python
  simba = dspy.SIMBA(metric=em, bsize=8, num_candidates=4, max_steps=3)
  best = simba.compile(qa, trainset=trainset)
  ```


## GEPA（teleprompt/gepa/gepa.py: GEPA）
- 设计思路
  - GEPA = Guided Experience Program Optimization：以“失败样本反馈→反思→提出改进指令→验证→（可选）Merge”循环的方式优化每个组件（predictor）。
  - 关键点：你的 `metric` 必须能接受 5 个参数 `(gold, pred, trace, pred_name, pred_trace)` 并可返回带反馈的 `ScoreWithFeedback`（score+feedback），否则 GEPA 会退化为仅以分数文本为反馈。
- 适用场景
  - 需要系统性的“带反馈的反思式提示改进”，尤其多组件程序；需要更强反思模型（reflection_lm）。
- 物料准备
  - `metric`（5参）、`trainset`、可选 `valset`、反思模型 `reflection_lm` 或自定义 `instruction_proposer`；预算：`auto` 或 `max_full_evals/max_metric_calls` 三选一。
- 最小 Demo（示意）
  ```python
  def metric_with_feedback(gold, pred, trace, pred_name, pred_trace):
      score = em(gold, pred, trace)
      return dict(score=score, feedback=f"score={score}")  # 或返回 dspy.teleprompt.gepa.gepa_utils.ScoreWithFeedback

  gepa = dspy.GEPA(
      metric=metric_with_feedback,
      auto="light",
      reflection_lm=your_reflection_lm,
      track_stats=True,
  )
  best = gepa.compile(qa, trainset=trainset, valset=trainset)
  ```


## KNNFewShot（teleprompt/knn_fewshot.py）
- 设计思路
  - 运行时对输入做向量化检索，选取 trainset 中最相似的 k 条作为 few-shot demos，再调用 `BootstrapFewShot` 在该子集上进行编译与推理（即“按需局部 few-shot”）。
- 适用场景
  - 输入分布多样且“就近示例”更有帮助；随输随取、轻量 few-shot。
- 物料准备
  - `k`、`trainset`、`vectorizer: dspy.clients.Embedder`（可包一层 sentence-transformers/自研 embedding）。
- 最小 Demo（示意）
  ```python
  from sentence_transformers import SentenceTransformer
  vec = dspy.Embedder(SentenceTransformer("all-MiniLM-L6-v2").encode)
  knn = dspy.KNNFewShot(k=3, trainset=trainset, vectorizer=vec)
  compiled = knn.compile(qa)
  print(compiled(question="..."))
  ```


## Ensemble（teleprompt/ensemble.py）
- 设计思路
  - 将若干已编译程序组成集合；预测时可随机抽取部分并通过 `reduce_fn` 聚合（如 `dspy.majority`）。
- 适用场景
  - 有多套不同风格/来源的已编译程序，想集成投票/聚合以提升稳健性。
- 物料准备
  - `programs: list[dspy.Module]`、可选 `reduce_fn` 与 `size`。
- 最小 Demo
  ```python
  ens = dspy.Ensemble(reduce_fn=dspy.majority, size=3)
  mixed = ens.compile([prog1, prog2, prog3, prog4])
  print(mixed(question="..."))
  ```


## BetterTogether（teleprompt/bettertogether.py）
- 设计思路
  - 将“提示优化（默认 BootstrapFewShotWithRandomSearch）”与“权重优化（默认 BootstrapFinetune）”交替执行，按策略串联：如 `p -> w -> p`；并在每步跟踪候选程序。
- 适用场景
  - 既想做提示工程，又希望最终落到权重微调；对顺序和相互促进有尝试空间。
- 物料准备
  - `metric`、`trainset`；学生程序所有预测器必须有 lm（用于微调）。
- 最小 Demo
  ```python
  dspy.settings.configure(lm=your_lm, experimental=True)
  bt = dspy.BetterTogether(metric=em)  # 默认 prompt_optimizer=BFRS, weight_optimizer=BootstrapFinetune
  compiled = bt.compile(qa, trainset=trainset, strategy="p -> w -> p", valset_ratio=0.1)
  ```


## AvatarOptimizer（teleprompt/avatar_optimizer.py）
- 设计思路
  - 面向 `dspy.Avatar` 多工具执行范式：先在训练集上运行 actor，按 metric 将正负样本与行动轨迹分桶；用一个“比较器”签名生成改进反馈；再据反馈生成“新指令”，迭代更新 actor 指令。
- 适用场景
  - 你的模块是 `Avatar` 这种“工具编排+行动”式 agent，且指标可以衡量成败；需要细化指令来改善负样本表现。
- 物料准备
  - `metric(gold, pred)->float`、`trainset`、`Avatar` 工具集；可设 upper/lower bound 过滤。
- 最小 Demo（示意）
  ```python
  from dspy.predict.avatar import Tool

  class QA(dspy.Signature):
      """Use tools then answer"""
      question = dspy.InputField()
      answer = dspy.OutputField()

  # 自定义工具
  tools = [Tool(name="Search", tool=your_search_impl, desc="web search")]
  actor = dspy.Avatar(QA, tools=tools)

  avo = dspy.AvatarOptimizer(metric=em, max_iters=3)
  best_actor = avo.compile(actor, trainset=trainset)
  ```


## GRPO（teleprompt/grpo.py，未在 __all__ 导出）
- 设计思路
  - Group Relative Preference Optimization：每步从多“教师/rollouts”收集一组候选，形成 `GRPOGroup`，基于相对偏好/奖励进行强化学习更新；学生 LM 在位更新或由后端管理。
- 适用场景
  - 后端 LM 支持“偏好优化/强化学习”接口；需要在 dspy 程序级别进行 RL 式优化。
- 物料准备
  - 学生/教师程序且“所有预测器必须有 lm”；`metric`；`num_rollouts_per_grpo_step` 等；`adapter`（一般 ChatAdapter）；训练步数等超参。
  - 需 LM 后端支持 `.reinforce()` 并能消费 `GRPOGroup` 训练数据。
- 最小 Demo（伪示例）
  ```python
  grpo = dspy.teleprompt.grpo.GRPO(metric=em, num_train_steps=50, num_rollouts_per_grpo_step=4, exclude_demos=True)
  # 确保 qa.set_lm(finetunable_rl_lm)
  trained = grpo.compile(qa, trainset=trainset, teacher=[qa])
  ```


---

## 常见问题与选择建议
- 只做提示工程：从 LabeledFewShot → BootstrapFewShot → RandomSearch / COPRO / MIPROv2 / SIMBA 逐步增强；追求系统最优与预算可控优先选 MIPROv2。
- 想用反馈/反思：GEPA（需 5 参 metric 与强 reflection_lm）；SIMBA 偏启发式自省；InferRules 更像“一次性提供可迁移规则”。
- 检索式 few-shot：KNNFewShot 适合长尾输入分布。
- 想权重学习：BootstrapFinetune；或选择 BetterTogether 将提示与权重交替优化。
- Agent/工具流：AvatarOptimizer 针对 Avatar。
- 需要集合投票：Ensemble。

## 运行注意
- finetune/GRPO 需后端 LM 支持相应接口；准备 `train_kwargs/adapter` 并确认线程/作业数量。
- GEPA 的 metric 签名与反馈返回结构需满足要求；reflection_lm 尽量强。
- Optuna/Embedding 等需安装额外依赖。
- 多数优化器支持并行评估（`num_threads`），但务必关注速率/预算与 token 开销。

