**一页速览（设计与SOP）**
- 设计思路：GEPA 是一种“反思驱动的进化式优化器”。它对 DSPy 程序中各个 predictor 的指令文本（instructions）进行迭代改写：先在一小批失败或表现欠佳的轨迹上“反思”，从评价函数的文本反馈中抽取可执行的改进要点，再用反思用的 LLM（或自定义 proposer）生成新指令候选；随后在验证集上评估并采用帕累托或当前最优策略保留强候选，持续迭代直至预算耗尽。
- 底层逻辑：
  - 轨迹捕获：运行程序并记录每次 predictor 调用及输入/输出，定位需要优化的 predictor 与其子轨迹。
  - 反馈汇总：调用用户提供的 metric（可返回分数与文字反馈）。若子预测器级反馈可用，GEPA优先用其文字反馈指导该 predictor 的指令演化。
  - 指令提案：使用反思 LLM 或自定义 ProposalFn，将“当前指令 + 失败样例+反馈”转化为“改进后指令”。
  - 候选选择与评估：对新候选进行评估，基于 Pareto/当前最优策略保留，循环进化。
- 需要的材料：
  - 可优化的 DSPy 程序（包含至少一个带 instructions 的 predictor）。
  - 评价函数 metric(gold, pred, trace, pred_name, pred_trace)：返回 float 或 {score, feedback}，其中 feedback 用于指导改写。
  - 训练集 trainset 与（尽量小但代表性强的）验证集 valset；无 valset 时将以 trainset 充当两者（更偏向“批量推理时的搜索”）。
  - 反思用 LLM（reflection_lm，建议强模型）或自定义 instruction proposer（如多模态场景）。
  - 预算/并发/日志配置：auto 或 max_metric_calls / max_full_evals、num_threads、log_dir、seed 等。
- 标准 SOP：
  1) 准备好 student 模块（含 predictor 指令）；
  2) 编写 GEPA 兼容的 metric；
  3) 准备 trainset 与小型代表性 valset；
  4) 选择强 reflection_lm（或提供自定义 proposer）；
  5) 实例化 GEPA（设定 auto/预算、candidate_selection、component_selector 等）；
  6) 调用 compile(student, trainset, valset) 获得优化后的程序；
  7) 如需分析，开启 track_stats 并查看 optimized_program.detailed_results；
  8) 用优化后的程序在测试/线上运行。

**GEPA 详细说明**
- 目标与对象
  - 目标：自动演化优化 DSPy 程序中的“可文本化组件”（主要是 predictor 的 instructions）。
  - 对象：student.named_predictors() 返回的各 predictor；GEPA 将维护 {component_name -> instruction_text} 的候选映射。

- 关键组件
  - 评价函数 metric（反馈入口）：
    - 签名：metric(gold, pred, trace, pred_name, pred_trace) → float | {score: float, feedback: str}
    - 作用：为整体程序与（在优化轮次中被选中）单个 predictor 提供分数与文字反馈。若仅返回分数，GEPA 会用“该轨迹得分为 X”的简要文本作为默认反馈。
    - 注：当前实现只使用模块级分数进行筛选；若 predictor 级分数与模块级分数不一致，将记录警告并以模块级分数为准（可通过 warn_on_score_mismatch 控制）。
  - DspyAdapter（桥接与数据生成）：
    - evaluate：按需用 Evaluate 或 bootstrap_trace_data 评估程序，捕获分数、输出与（可选）轨迹。
    - make_reflective_dataset：从轨迹里为每个待优化的 predictor 汇总“Inputs / Generated Outputs / Feedback”。
      - 解析失败时（如输出无法按签名解析），可注入结构化指导提示（add_format_failure_as_feedback）。
      - 选择用于反思的样本：优先包含失败样本，否则在该 predictor 多次调用中随机抽取一个代表性实例。
    - propose_new_texts：默认使用 GEPA 内置提案器；如传入自定义 proposer（见下），则在 reflection_lm 上下文中调用它生成新指令。
  - 指令提案（Instruction Proposal）：
    - 反思模型 reflection_lm：用于把“当前指令+失败样例+反馈”转为“新指令文本”。模型越强，反思和改写质量越好。
    - 自定义 proposer：传入实现 ProposalFn 的对象即可替换内置提案逻辑；库内提供 MultiModalInstructionProposer 来保留 dspy.Image 等多模态对象并生成更贴合多模态的改写。
  - 候选选择与合并：
    - candidate_selection_strategy："pareto"（基于验证集多任务帕累托采样，增强多解探索）或 "current_best"（更贪心）。
    - use_merge：可对候选进行“合并”以融合优点（最大合并次数 max_merge_invocations）。

- 运行流程与细节
  - 组件选择（component_selector）：
    - 'round_robin'（默认）：轮换优化各 predictor；'all'：同时选中全部；亦可传入自定义 ReflectionComponentSelector（可用 LLM 驱动更智能的选择）。
  - 预算控制：
    - 只能三选一：auto（"light"|"medium"|"heavy"）、max_full_evals、max_metric_calls。
    - auto 会依据 predictor 数、候选规模及 valset 大小估算 max_metric_calls（内含周期性全量评估与小批量反思评估）。
  - 数据与分数：
    - perfect_score 用于早停/跳过完美样本（skip_perfect_score）；failure_score 为解析失败的惩罚分；Evaluate 支持并发（num_threads）。
  - 轨迹与反馈：
    - trace 结构为 [(predictor, predictor_inputs, predictor_output), ...]；pred_trace 是其中属于目标 predictor 的子序列。
    - GEPA 以文字反馈引导“指令改写”，因此 metric 的反馈质量至关重要（可程序级，也可 predictor 级）。
  - 记录与复现：
    - log_dir 持久化候选、分数、检查点，可断点续跑；use_wandb/use_mlflow 做可视化；seed 控制随机性。
  - 推理期批处理搜索技巧：
    - 设置 valset=trainset、track_stats=True、track_best_outputs=True，即可把 GEPA 用作一次性批量搜索：detailed_results.best_outputs_valset 提供每个任务的最好输出；val_aggregate_scores 构成该批次的“帕累托前沿”。

- 配置建议
  - 反思模型：优先使用上下文窗口大、推理强的模型（如 gpt-4o/gpt-5 级）。
  - 验证集：尽量小但能代表分布，用于维持泛化；训练集尽可能大以供反思。
  - 多模态：使用 MultiModalInstructionProposer 保留图像/复杂类型输入，提案更稳。
  - 分数一致性：若使用语义/判官类指标，分数可能有波动；必要时关闭 warn_on_score_mismatch 以避免噪声告警。

**最小 Demo（可直接改造运行）**
```python
import dspy
from dspy.teleprompt import GEPA

# 1) 定义签名与模块
class AddTwo(dspy.Signature):
    """Solve the arithmetic problem and return only the integer answer in `result`."""
    question = dspy.InputField(desc="e.g., 27 + 15")
    result = dspy.OutputField(desc="integer answer")

class Adder(dspy.Module):
    def __init__(self):
        super().__init__()
        self.solve = dspy.Predict(AddTwo)

    def forward(self, question: str):
        return self.solve(question=question)

# 2) 配置基础与反思用 LLM（示例，按需替换模型与密钥）
lm = dspy.LM(model="gpt-4o-mini", temperature=0.2)
reflection_lm = dspy.LM(model="gpt-4o-mini", temperature=0.8, max_tokens=2048)
dspy.settings.configure(lm=lm)

# 3) 构造少量训练/验证数据
trainset = [
    dspy.Example(question="27 + 15", result="42").with_inputs("question"),
    dspy.Example(question="3 + 19", result="22").with_inputs("question"),
]
valset = [
    dspy.Example(question="8 + 7", result="15").with_inputs("question"),
]

# 4) 定义 GEPA 兼容 metric（返回分数与文字反馈）
def metric(gold: dspy.Example,
           pred: dspy.Prediction,
           trace=None,
           pred_name: str | None = None,
           pred_trace=None):
    gold_ans = str(gold.result).strip()
    got = str(pred.get("result", "")).strip()
    ok = int(got == gold_ans)
    fb = "Correct." if ok else f"Wrong. Expected {gold_ans} but got {got}. Return only the integer in `result`."
    return {"score": float(ok), "feedback": fb}

# 5) 实例化待优化程序与 GEPA
student = Adder()
gepa = GEPA(
    metric=metric,
    auto="light",                  # 或者指定 max_metric_calls / max_full_evals
    reflection_lm=reflection_lm,    # 反思用模型
    candidate_selection_strategy="pareto",  # 或 "current_best"
    component_selector="round_robin",
    skip_perfect_score=True,
    track_stats=True,               # 需要详细结果时打开
)

# 6) 运行优化
optimized = gepa.compile(student, trainset=trainset, valset=valset)

# 7) 查看优化后的指令
for name, pred in optimized.named_predictors():
    print(name, "\n---\n", pred.signature.instructions)

# 8) （可选）检查详细结果/帕累托前沿
if hasattr(optimized, "detailed_results") and optimized.detailed_results is not None:
    print("Pareto scores:", optimized.detailed_results.val_aggregate_scores)
```

补充：如需对包含 dspy.Image 等多模态输入的任务进行反思式指令改写，可在 GEPA 中传入 instruction_proposer=dspy.teleprompt.gepa.instruction_proposal.MultiModalInstructionProposer()（在提供 reflection_lm 时，GEPA 会自动在该 LLM 上下文中调用 proposer）。

