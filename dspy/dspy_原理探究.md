如何截取到最终通过 LiteLLM 发送的系统提示词（system prompt）

- 结论：最稳妥的做法是在 DSPy 回调里截获 LM 调用时的 `messages`，直接读取其中 `role` 为 `system`（或 responses API 下的 `developer`）的内容。这一步发生在真正调用 LiteLLM 之前，能拿到最终要发出的消息体。若想看 LiteLLM 对不同厂商的最终落地请求，可同时打开 LiteLLM 的 DEBUG 日志。

关键代码定位（便于理解“最终消息”构造链路）

- 构造 messages 的位置：`adapters/base.py:215` 起，`Adapter.format()` 组装 `system` + few-shot + history + 当前输入，最后返回一个标准的 `messages` 列表。
  - `adapters/base.py:221` 明确把系统提示词放到 `{"role": "system", "content": system_message}`。
  - `ChatAdapter` 具体如何拼装系统提示词：
    - 字段描述：`adapters/chat_adapter.py:69`
    - 字段结构：`adapters/chat_adapter.py:75`
    - 任务说明：`adapters/chat_adapter.py:97`
- LM 发送请求前的最终打包：`clients/lm.py:154`-`clients/lm.py:158`，把 `model`、`messages` 和其他参数打成 `request` 传给 LiteLLM。
- BaseLM 里历史记录的来源：`clients/base_lm.py:65`-`clients/base_lm.py:79` 会把本次调用的 `messages`、`outputs` 等写入 `history`，`inspect_history()` 就是打印这个。
- ReAct 的系统提示来源：`predict/react.py:52`-`predict/react.py:73` 会把工具列表等写进 `signature.instructions`，随后由 `ChatAdapter` 拼到系统提示词里。

为什么你看到的 `dspy.inspect_history()` 里 ReAct 的提示词“不完整”

- ReAct 每次调用里通常有两类 LM 调用：
  1) 选择下一步（react 步）；2) 最终抽取答案（extract 步）。
  - `forward()` 的最后一次 LM 调用是 extract，因此 `inspect_history(n=1)` 往往显示的是 extract 步的系统提示词（没有工具清单），而不是你关心的 react 步。
  - 解决：用 `inspect_history(n=2+)` 查看更早一条，或用下述回调拦截每一次 LM 调用的 `messages`，就能拿到 react 步那条包含工具清单的系统提示词。

推荐做法一：用 DSPy 回调精确截取系统提示词（推荐）

思路：`BaseLM.__call__` 被 `with_callbacks` 包装，`on_lm_start` 能拿到本次 LM 调用的参数，其中 `messages` 就是要发给 LiteLLM 的最终消息。对于 OpenAI Responses API 且 `use_developer_role=True` 的情况，`LM.forward()` 会把 `system` 改成 `developer`（`clients/lm.py:136`-`clients/lm.py:140`），所以回调里同时兼容这两种角色。

示例代码（直接放到你项目里使用）：

```python
import dspy
from dspy.utils.callback import BaseCallback

def _flatten_message_content(content):
    # content 可能是 str 或 list（多模态块）。这里只取文本块，方便打印/存档。
    if isinstance(content, str):
        return content
    if isinstance(content, list):
        parts = []
        for c in content:
            if isinstance(c, dict) and c.get("type") == "text":
                parts.append(c.get("text", ""))
        return "\n".join(p for p in parts if p)
    return str(content)

class SystemPromptCapture(BaseCallback):
    def __init__(self):
        self.last_system_prompt = None
        self.all_system_prompts = []  # 如果需要每次调用都存下来

    def on_lm_start(self, call_id, instance, inputs):
        messages = inputs.get("messages") or (
            [{"role": "user", "content": inputs.get("prompt")}]
        )
        # 兼容 responses API 下的 developer 角色
        sys_like = [m for m in messages if m.get("role") in ("system", "developer")]
        if sys_like:
            content = _flatten_message_content(sys_like[0].get("content"))
            self.last_system_prompt = content
            self.all_system_prompts.append(content)
            # 你可以在此处写文件/打日志
            print("[System Prompt]\n" + content)

# 启用回调
cap = SystemPromptCapture()
dspy.settings.configure(callbacks=[cap])

# 之后正常跑你的程序（包含 ReAct）即可在控制台看到每次最终发出的系统提示词
```

如需更早地（在适配器格式化完成后）截取，也可以实现 `on_adapter_format_end`，其 `outputs` 就是 `Adapter.format()` 产出的 `messages`：

```python
class AdapterTap(BaseCallback):
    def on_adapter_format_end(self, call_id, outputs, exception):
        if exception is not None:
            return
        messages = outputs
        sys_msgs = [m for m in messages if m.get("role") == "system"]
        if sys_msgs:
            print("[Adapter System]\n" + _flatten_message_content(sys_msgs[0]["content"]))
```

推荐做法二：打开 LiteLLM 调试日志（观察 provider 侧最终请求）

- DSPy 已封装了开关：`clients/__init__.py:93`-`clients/__init__.py:100`。
- 使用：

```python
import dspy
dspy.enable_litellm_logging()  # DEBUG 级别。想关闭：dspy.disable_litellm_logging()
```

开启后，LiteLLM 会把它送往各家厂商（含可能的字段转换）的请求打印到日志，便于核对“最终入厂商 API 的形态”。

补充：text 模式与 responses 模式的差异

- chat 模式：直接传 `messages` 给 LiteLLM（参考 `clients/lm.py:342`-`clients/lm.py:355`）。
- text 模式：DSPy 会把 `messages` 拼接成一段 `prompt` 字符串再调用 `litellm.text_completion`（`clients/lm.py:371`-`clients/lm.py:383`）。此时“系统提示词”已经并入到单个字符串里，仍可在回调中拿到拼接前的 `messages`。
- responses 模式：若 `use_developer_role=True`，会把 `system` 改成 `developer` 再发送（`clients/lm.py:136`-`clients/lm.py:140`）。回调中请同时匹配 `system`/`developer`。

小结

- 真正稳定可靠的截取点是：在 LM 调用开始（`on_lm_start`）读取 `messages`，提取 `system`/`developer` 内容；这样不依赖 `inspect_history()` 的呈现，也不受 ReAct 多阶段调用的干扰。
- 若你只是没有看到 ReAct 的工具清单，记得 `inspect_history(n=1)` 一般对应 extract 步；把 `n` 调大或用回调逐次捕获，即可看到 react 步那条完整系统提示词。
