# DSPy 闭环任务（签名设计 → 评估 → 优化）核心组件协同解析

## 闭环流程概览
闭环 DSPy 流程通常包含四个阶段：
1. **签名设计**：使用 `Signature` 描述输入/输出字段与说明，指导语言模型生成的格式和语义。
2. **模块实现**：基于签名构造 `Module`（通常组合多个 `Predict`/子模块），定义任务逻辑。
3. **评估**：通过 `Evaluate` 在 `Dataset` 提供的 `Example` 上调用模块，并用 `Metric` 计算表现。
4. **优化**：`Teleprompter`（优化器）根据评估反馈调优模块（如选择示例、改写指令），再次评估直至达到目标。

上述步骤构成了“设计 → 验证 → 调整”的循环，每个阶段的数据结构契约确保信息可以在组件间无缝流动。

## 关键组件与数据结构

### Signature：任务接口约束
- 通过 `SignatureMeta.__call__` 将字符串或字段字典动态转换为 Pydantic 子类 [`signatures/signature.py:40`](signatures/signature.py:40)。
- `Signature.input_fields` 与 `output_fields` 按 `InputField`/`OutputField` 分类字段，保留顺序 [`signatures/signature.py:200`](signatures/signature.py:200)。
- 每个字段在 `json_schema_extra` 中记录 `prefix`、`desc` 及类型元信息，用于提示词构造与解析 [`signatures/signature.py:174`](signatures/signature.py:174)。
- `ensure_signature` 允许模块同一套逻辑接受字符串签名或已定义的类，并保证说明同步 [`signatures/signature.py:358`](signatures/signature.py:358)。

### Module：任务逻辑载体
- `Module` 基于 `ProgramMeta` 自动注入 `_compiled`、`callbacks`、`history` 等状态 [`primitives/module.py:18`](primitives/module.py:18)。
- 核心执行入口 `Module.__call__` 会在 `settings.context` 下调用 `forward` 并统一返回 `Prediction`，必要时记录 Token 使用 [`primitives/module.py:65`](primitives/module.py:65)。
- `Module.named_predictors()` 让上层可以遍历内部所有 `Predict` 参数，支撑优化器批量更新提示或 LM 实例 [`primitives/module.py:109`](primitives/module.py:109)。
- `Module.batch` 展示了如何把一批 `Example` 的输入映射到 `forward` 调用（借助 `Example.inputs()` 与 `Parallel`）[`primitives/module.py:146`](primitives/module.py:146)。

### Example / Prediction：运行时数据容器
- `Example` 是轻量级 key-value 容器，支持属性、下标访问，还能声明输入字段集合 `with_inputs` 并在 `inputs()` 中裁剪 [`primitives/example.py:1`](primitives/example.py:1)。
- `Prediction` 继承自 `Example`，去掉演示字段 `_demos` 并记录原始 completions 与 LM usage，用于返回模型输出 [`primitives/prediction.py:1`](primitives/prediction.py:1)。
- `Prediction.from_completions` 利用签名把 LM 返回的 completion 映射为字段值，确保输出和签名保持一一对应 [`primitives/prediction.py:17`](primitives/prediction.py:17)。

### Dataset：示例提供者
- `Dataset` 负责根据随机种子和大小裁剪底层数据，并把原始字典包装成带 `dspy_uuid`/`dspy_split` 的 `Example` 对象 [`datasets/dataset.py:8`](datasets/dataset.py:8)。
- 如果在构造时指定 `input_keys`，会在 `_shuffle_and_sample` 中自动调用 `with_inputs`，保证后续 `Example.inputs()` 可正确分离输入/标签 [`datasets/dataset.py:60`](datasets/dataset.py:60)。

### Metric：评估准则
- 评估指标是简单的 Python 函数，例如 `answer_exact_match` / `F1`，接口统一为 `(example, prediction, trace=None)` 并返回分数或布尔值 [`evaluate/metrics.py:11`](evaluate/metrics.py:11)。
- 常用指标内部会解析 `Example`/`Prediction` 字段实现匹配、归一化等逻辑 [`evaluate/metrics.py:57`](evaluate/metrics.py:57)。

### Evaluation：闭环中期的度量器
- `Evaluate` 接受 `devset`、`metric`、并发配置等，在调用时构造 `ParallelExecutor` 遍历数据集 [`evaluate/evaluate.py:64`](evaluate/evaluate.py:64)。
- 每个样本通过 `example.inputs()` 以关键字参数形式送入模块，返回的 `Prediction` 与原 `Example` 一起交给 `metric` 评分 [`evaluate/evaluate.py:170`](evaluate/evaluate.py:170)。
- 结果封装为 `EvaluationResult`（继承 `Prediction`），包含总分与逐样本 `(example, prediction, score)` 列表，便于后续分析或优化器读取 [`evaluate/evaluate.py:48`](evaluate/evaluate.py:48)。

#### Evaluation 调试视角示例
以下用一个中文问答小任务展示 `Evaluate` 如何把输入沿着管线流转成最终得分。所有数字都是真实示例值，便于对照理解：

1. **准备阶段（数据、模块、指标）**
   - **签名**：`question -> answer`，要求输入字段为 `question`，输出字段为 `answer`。
   - **验证集 `devset`**（两个 `Example`）：
     ```python
     Example(question="法国的首都是哪里？", answer="巴黎").with_inputs("question")
     Example(question="2+2 等于几？", answer="4").with_inputs("question")
     ```
     `.with_inputs("question")` 把 `question` 标记为输入，剩余字段（如 `answer`）自动视为标签。
   - **学生模块 `program`**：内部调用一个 `Predict`，为了方便跟踪，这里让它返回固定的演示预测：
     ```python
     def forward(self, question):
         if "首都" in question:
             return dspy.Prediction(answer="巴黎")
         return dspy.Prediction(answer="5")  # 第二题故意出错
     ```
   - **指标函数 `metric`**：使用 `answer_exact_match`，当预测答案完全相同返回 `1.0`，否则 `0.0` [`evaluate/metrics.py:141`](evaluate/metrics.py:141)。

2. **Evaluate 初始化**
   - `Evaluate(devset=..., metric=answer_exact_match, display_progress=False)` 仅保存引用，不做运算。
   - 重要状态：`self.devset` 是长度为 2 的列表，`self.metric` 指向函数对象，`self.num_threads` 默认为 `None`。

3. **调用 `result = evaluate(program)`**
   - `evaluate.__call__` 进入时把可选参数全部补齐；`devset` 仍是那两个 `Example`。
   - 创建 `ParallelExecutor`，此例因为 `num_threads=None`，后续会以单线程顺序执行。
   - 内部定义 `process_item(example)`，其行为等价于：
     ```python
     prediction = program(**example.inputs())
     score = metric(example, prediction)
     return prediction, score
     ```

4. **处理第一个样本**
   - `example.inputs()` 返回 `{"question": "法国的首都是哪里？"}`。
   - `program(question=...)`：模块命中“首都”分支，产出 `Prediction(answer="巴黎")`。
   - `metric(example, prediction)`：标签是 `example.answer="巴黎"`，完全匹配，得分 `1.0`。
   - `process_item` 返回 `(Prediction(answer="巴黎"), 1.0)`。

5. **处理第二个样本**
   - `example.inputs()` 返回 `{"question": "2+2 等于几？"}`。
   - 模块走到默认分支，返回 `Prediction(answer="5")`。
   - `metric` 对比标签 `"4"`，不匹配，得分 `0.0`。
   - `process_item` 返回 `(Prediction(answer="5"), 0.0)`。

6. **结果汇总与封装**
   - `ParallelExecutor.execute` 收集到的 `results` 列表等价于：
     ```python
     [
         (Prediction(answer="巴黎"), 1.0),
         (Prediction(answer="5"), 0.0),
     ]
     ```
   - `Evaluate` 把它与原始 `devset` zip 成逐条三元组：
     ```python
     [
         (Example(...巴黎...), Prediction(answer="巴黎"), 1.0),
         (Example(...2+2...), Prediction(answer="5"), 0.0),
     ]
     ```
   - 计算总分：`ncorrect = 1.0 + 0.0 = 1.0`，`ntotal = 2`，平均得分 `score = 1.0 / 2 = 0.5`。
   - 构造 `EvaluationResult(score=0.5, results=<上述列表>)`，其中 `score` 字段可直接用于比较不同模型，`results` 列表能进一步定位错例。

7. **调试输出（可选）**
   - 若传入 `display_table=True`，`Evaluate` 会尝试把 `results` 转为 DataFrame 以供可视化；若配置 `save_as_json` 等，会序列化同样的数据结构。

通过这个流程可以直观看到：每个 `Example` 的输入字典如何一路传入模块、产出 `Prediction`、被指标函数打分，再汇总到最终的 `EvaluationResult`。当更换模块或指标时，只要遵守同样的输入/输出契约，Evaluation 就能像上述“调试日志”一样给出逐条记录，帮助定位问题并驱动后续优化。

### Optimize / Teleprompter：自动调优驱动
- DSPy 把各种优化策略统一抽象为 `Teleprompter.compile(student, trainset=..., ...)`，入参为待调优模块、训练集及可选教师 [`teleprompt/teleprompt.py:7`](teleprompt/teleprompt.py:7)。
- 以 `BootstrapFewShot` 为例：
  - `compile` 会复制学生/教师模块，收集训练示例轨迹并挑选示例填充到 `Predict.demos` [`teleprompt/bootstrap.py:81`](teleprompt/bootstrap.py:81)。
  - 在 `_bootstrap_one_example` 中调用教师模块预测，依赖签名解析预测结果并保存 trace 供筛选使用 [`teleprompt/bootstrap.py:179`](teleprompt/bootstrap.py:179)。
- 更复杂的 `BootstrapFewShotWithRandomSearch` 会对多个候选程序运行 `Evaluate`，比较指标分数挑选最佳方案，并把候选列表附着在返回的学生模块上，形成可回顾的搜索记录 [`teleprompt/random_search.py:27`](teleprompt/random_search.py:27)。

## 数据流动路径与契约
1. **示例准备**：`Dataset` 生成的 `Example` 设置了输入键，确保 `Example.inputs()` 返回的键值对能被 `Module.__call__` 正确解包为 `forward` 的命名参数。
2. **签名约束**：每个 `Predict` 在构造时通过 `ensure_signature` 拿到 `Signature` 元类定义，输入字段缺失时会在 `_forward_preprocess` 内警告 [`predict/predict.py:104`](predict/predict.py:104)；输出则由 `Prediction.from_completions` 按签名字段落盘。
3. **模块执行**：`Module.__call__` 统一了调用约定，既可以直接返回 `Prediction`，也能在追踪模式下附带 token 使用信息供优化器或调试记录分析 [`primitives/module.py:65`](primitives/module.py:65)。
4. **评估循环**：`Evaluate` 对 dev 集逐条执行 `program(**example.inputs())`，指标函数读取 `example.labels()` 中的标签字段与 `prediction` 对比，返回浮点评分或布尔值；所有结果累积成 `EvaluationResult`。
5. **优化反馈**：优化器读取 `EvaluationResult.score` / `results`，或直接在编译过程中调用度量函数筛选示例，更新学生模块的签名指令、LM 配置、demo 列表等，并将 `_compiled` 标记为 `True` 表示可直接投入推理 [`teleprompt/bootstrap.py:88`](teleprompt/bootstrap.py:88)。

此契约让每个环节都能只关心自身职责：Signature 描述接口、Module 实现逻辑、Example/Dataset 传递数据、Evaluation/Metric 量化效果、Optimize 根据量化结果迭代参数。

## 典型协同场景
- **Few-shot 设定**：Dataset 提供带标签的 `Example`；`BootstrapFewShot` 采集表现最好的 trace 作为提示词示例；`Evaluate` 使用同一指标验证改写后的模块效果，形成提升闭环。
- **签名迭代**：若优化器（如 `GEPA` 或 `SignatureOptimizer`）需要改写说明，则根据 `EvaluationResult` 中的失败案例调整 `Signature.instructions` 或字段提示，再由 Module/Prediction 自动继承新约束。
- **大规模搜索**：`BootstrapFewShotWithRandomSearch` 类优化器批量生成候选模块，利用 `Evaluate` 并行评估，在可控预算内寻找最优策略，同时保留候选得分供人工审查或继续微调。

## 设计与调试提示
- 保持 `Signature` 中输入字段与 `Example.with_inputs()` 定义一致，否则会在 `Predict._forward_preprocess` 触发缺失警告并导致指标计算异常。
- 指标函数最好返回数值型分数，便于优化器比较；布尔指标建议转为 `float`（True=1.0/False=0.0），与 `EvaluationResult.score` 的聚合逻辑保持一致。
- 优化器返回的模块会被标记 `_compiled=True`，后续若需继续训练/搜索，应调用 `reset_copy()` 以免 demo/梯度等状态污染新一轮实验。

通过上述协同机制，DSPy 能在统一的数据结构和接口约束下，实现从任务建模到自动调优的完整闭环流程。
