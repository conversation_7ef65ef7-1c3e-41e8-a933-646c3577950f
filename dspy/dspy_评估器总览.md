# DSPy 评估器总览（基于本仓库代码）

本文基于当前仓库的 `dspy.evaluate` 代码整理，可用的评估器/指标与其适用场景与最小示例如下。评估时一般通过 `Evaluate` 将“程序 + 指标函数/模块”组合在一起运行。

## 通用评估引擎

- 名称: `dspy.evaluate.Evaluate`
- 场景: 对任意 `dspy.Module` 的程序在开发集 `devset` 上进行并行评测。需要提供一个“指标（metric）”，其返回布尔值（正确/错误）或 [0,1] 的分数。
- 最小示例:

```python
import dspy
from dspy.evaluate import Evaluate, answer_exact_match

# 一个最小的可评估程序：输入 question，输出 answer
class QAProgram(dspy.Module):
    def forward(self, question):
        return dspy.Prediction(answer="Paris")

program = QAProgram()

# 开发集，每个样本包含 question 与期望的 answer
devset = [
    dspy.Example(question="Capital of France?", answer="Paris").with_inputs("question"),
]

evaluate = Evaluate(devset=devset, metric=answer_exact_match, display_table=False)
result = evaluate(program)
print(result.score)  # 百分比，例如 100.0
```

---

## 指标函数型评估器（基于符号/字符串匹配）

这些评估器本质是“指标函数”，常用于分类/抽取/短答案问答等任务，通常与 `Evaluate` 一起使用。

### 1) `answer_exact_match`
- 导入: `from dspy.evaluate import answer_exact_match`
- 场景: 问答/分类等任务，对比 `pred.answer` 与 `example.answer` 是否匹配（支持多参考答案）。可通过参数 `frac` 设置基于 F1 的松弛匹配阈值（默认 1.0 即严格 EM）。
- 最小示例:

```python
from dspy.evaluate import Evaluate, answer_exact_match

class QA(dspy.Module):
    def forward(self, question):
        return dspy.Prediction(answer="Albert Einstein")

devset = [
    dspy.Example(question="Who proposed relativity?", answer=["Einstein", "Albert Einstein"]).with_inputs("question"),
]

metric = answer_exact_match                      # 严格精确匹配（EM）
# 或: metric = lambda ex, pred: answer_exact_match(ex, pred, frac=0.8)  # F1>=0.8 即视为正确

result = Evaluate(devset=devset, metric=metric)(QA())
print(result.score)
```

### 2) `answer_passage_match`
- 导入: `from dspy.evaluate import answer_passage_match`
- 场景: 检索/检索增强生成（RAG）等任务，判断预测的 `pred.context`（应为 `list[str]`）中是否包含参考答案。
- 最小示例:

```python
from dspy.evaluate import Evaluate, answer_passage_match

class Retriever(dspy.Module):
    def forward(self, question):
        passages = [
            "... Einstein developed the theory of relativity ...",
            "... other content ...",
        ]
        return dspy.Prediction(context=passages)

devset = [
    dspy.Example(question="Who proposed relativity?", answer="Einstein").with_inputs("question"),
]

result = Evaluate(devset=devset, metric=answer_passage_match)(Retriever())
print(result.score)
```

### 3) 基础文本匹配指标（直接分数函数）
- 位置: `dspy/evaluate/metrics.py`
- 导入（直接从文件）: `from dspy.evaluate.metrics import EM, F1, HotPotF1, precision_score`
- 场景: 需要直接对两个字符串进行打分（不走 `example/pred` 封装）或自定义封装到 `Evaluate` 的 `metric`。注意：`EM/F1/HotPotF1/precision_score` 接口签名为 `(prediction: str, ground_truth(s))`，若在 `Evaluate` 中使用，请用 `lambda` 封装。
- 最小示例（与 `Evaluate` 结合）:

```python
from dspy.evaluate import Evaluate
from dspy.evaluate import answer_exact_match  # 推荐封装
from dspy.evaluate.metrics import EM, F1, HotPotF1, precision_score

class QA(dspy.Module):
    def forward(self, question):
        return dspy.Prediction(answer="San Francisco")

devset = [
    dspy.Example(question="Where is the Golden Gate Bridge?", answer=["San Francisco", "SF"]).with_inputs("question"),
]

# 方式A：推荐使用已封装好的 answer_exact_match
res_em = Evaluate(devset=devset, metric=answer_exact_match)(QA())

# 方式B：自定义封装底层指标函数
res_f1 = Evaluate(
    devset=devset,
    metric=lambda ex, pred: max(F1(pred.answer, [ex.answer] if isinstance(ex.answer, str) else ex.answer), 0.0),
)(QA())

print(res_em.score, res_f1.score)
```

> 提示：`normalize_text`（导入 `from dspy.evaluate import normalize_text`）为字符串归一化工具，常用于自定义指标时的预处理。

---

## LLM 评审型评估器（自动语义评估）

这些评估器本身就是 `dspy.Module`，会调用语言模型对“系统输出 vs 参考答案/证据”进行语义对齐评审，更适合开放式生成、摘要、长答案问答、RAG 等需要语义层面判断的任务。

### 4) `SemanticF1`
- 导入: `from dspy.evaluate import SemanticF1`
- 场景: 针对开放式问答/摘要等，比较 `pred.response` 与参考 `example.response` 在“关键要点”上的召回与精度，并计算 F1；`decompositional=True` 时先显式列举要点再评审。
- 输入/输出要求: `example` 需包含 `question` 与参考 `response`；`program` 需产出 `response` 字段。
- 最小示例:

```python
from dspy.evaluate import Evaluate, SemanticF1

class Writer(dspy.Module):
    def forward(self, question):
        return dspy.Prediction(response="The capital of France is Paris.")

devset = [
    dspy.Example(
        question="What is the capital of France?",
        response="Paris is the capital of France.",
    ).with_inputs("question"),
]

metric = SemanticF1(threshold=0.66, decompositional=False)
result = Evaluate(devset=devset, metric=metric)(Writer())
print(result.score)
```

### 5) `CompleteAndGrounded`
- 导入: `from dspy.evaluate import CompleteAndGrounded`
- 场景: RAG/检索增强生成等，需要同时评估回答的“完整性（覆盖参考要点）”与“扎实度/可溯源性（回答是否被检索到的上下文支持）”。
- 输入/输出要求: `example` 需包含 `question` 与参考 `response`；`program` 需产出 `response` 与 `context`（字符串，作为已检索到的证据文本）。
- 最小示例:

```python
from dspy.evaluate import Evaluate, CompleteAndGrounded

class RAGProgram(dspy.Module):
    def forward(self, question):
        response = "Paris is the capital of France."
        context  = "France's capital city is Paris, known for the Eiffel Tower."
        return dspy.Prediction(response=response, context=context)

devset = [
    dspy.Example(
        question="What is the capital of France?",
        response="Paris is the capital of France.",
    ).with_inputs("question"),
]

metric = CompleteAndGrounded(threshold=0.66)
result = Evaluate(devset=devset, metric=metric)(RAGProgram())
print(result.score)
```

> 注意：`CompleteAndGrounded` 期望 `pred.context` 为字符串；而 `answer_passage_match` 期望 `pred.context` 为 `list[str]`。二者场景与输入约定不同，请按需选择。

---

## 小结与选型建议

- 结构化/短答案任务：优先用 `answer_exact_match`，必要时用 `frac<1.0` 允许 F1 近似匹配。
- 检索命中判断：用 `answer_passage_match`（`pred.context` 为 `list[str]`）。
- 开放式生成/摘要：用 `SemanticF1` 做语义层面对齐评估。
- RAG 质量（覆盖度+可溯源）：用 `CompleteAndGrounded`。
- 如需完全自定义：可基于 `dspy.evaluate.metrics` 下的 `EM/F1/HotPotF1/precision_score` 组合封装自定义 `metric`。
