# DSPy ReAct 预测器：设计思路与工作机制

本文基于当前代码库实现进行梳理（核心代码见 `predict/react.py`，并结合 `adapters/*`、`adapters/types/tool.py`、`predict/predict.py`、`predict/chain_of_thought.py` 等文件），系统说明 dspy.ReAct 的设计理念、提示词构造、运行流程、类型与工具机制、上下文管理与可定制点，以及与 DSPy 生态（History、Streaming、优化器）的配合要点。

---

## 1. 设计理念（Reasoning + Acting，签名多态，双阶段）

- Reasoning + Acting：ReAct 将“推理（思考）”与“行动（调用工具）”组合成循环。在每步中先产出思考，再选择一个工具与其 JSON 参量，执行得到观测，累积为轨迹。
- 签名多态（Signature polymorphism）：ReAct 不限定任务形态，直接接受任意 `dspy.Signature`（如 `"question -> answer"` 或带更多输入/输出字段的签名），并在此基础上派生出用于“选下一步”的内部签名。
- 双阶段结构：
  - react 阶段：用 `dspy.Predict` 在扩展签名上产生 `next_thought`、`next_tool_name`、`next_tool_args`。
  - extract 阶段：用 `dspy.ChainOfThought` 在“回退（fallback）签名”上读取完整轨迹并抽取最终输出（例如 `answer`）。
- 通用性优先于厂商特性：ReAct 默认以“文本提示 + 强类型输出”实现工具选择与参数结构化，而非强绑定某厂商的原生 function calling（见下文对比）。

---

## 2. 代码结构与关键成员

- 入口类：`class ReAct(Module)`（文件：`predict/react.py`）
- 关键属性：
  - `self.tools: dict[str, Tool]`：工具名到 `dspy.Tool` 的映射（用户传入的函数会被包装成 `Tool`）。
  - `self.react: dspy.Predict`：选下一步（thought/name/args）的预测器。
  - `self.extract: dspy.ChainOfThought`：最终抽取器，依据轨迹生成任务输出。
  - `self.max_iters: int`：最大迭代步数，默认 10，可在调用时用 `max_iters` 覆盖。
- 构造时的重要步骤：
  1) 将传入工具统一包装为 `dspy.Tool`，再放入 `dict` 便于索引；
  2) 向工具集合中自动追加一个内置工具 `finish`（表示完成）；
  3) 构造两份签名：
     - react_signature：在基础签名的 `instructions` 上拼接 ReAct 通用指导与工具清单，追加字段
       - `trajectory: str`（Input），
       - `next_thought: str`（Output），
       - `next_tool_name: Literal[...]`（Output，枚举所有工具名），
       - `next_tool_args: dict[str, Any]`（Output，JSON 参量）；
     - fallback_signature：作为抽取阶段的签名（基础签名的输入+输出）并追加 `trajectory: str`（Input）。

---

## 3. 提示词与消息构造（ChatAdapter + 强类型约束）

- 系统提示词（system）由适配器统一构造：`adapters/base.py` 与 `adapters/chat_adapter.py`。其结构通常为：
  - 字段说明（input/output 列表与类型约束），
  - 字段结构（要求输出格式遵循 `[[ ## field_name ## ]] ... [[ ## completed ## ]]` 片段），
  - 任务说明（即签名的 `instructions`）。
- ReAct 对 `instructions` 的扩展（见 `predict/react.py`）：
  - 注入任务目标：给定输入字段，用“工具清单”收集生成输出字段所需的信息；
  - 交互规范：每步交替产出 `next_thought`、`next_tool_name`、`next_tool_args`；工具调用后会获得 `observation` 并附加到 `trajectory`；
  - 强约束：`next_tool_name` 必须是提供清单中的一个；`next_tool_args` 必须是 JSON；
  - 工具清单逐条枚举：`(1) {tool}`、`(2) {tool}`…，其中 `Tool.__str__` 会展开“名称/描述/参数 schema”。
- 强类型输出约束：
  - `next_tool_name` 使用 `typing.Literal[...]`（见 `adapters/utils.translate_field_type`），系统提示词会明确“必须精确匹配其中一个值”；
  - `next_tool_args` 使用 `dict[str, Any]`，并在文案里强调“必须为合法 JSON”；
  - 适配器负责将 LM 输出解析回对应类型（`adapters/utils.parse_value`）。
- 轨迹渲染：ReAct 将内部 `trajectory`（一个 dict，如 `thought_0/tool_name_0/tool_args_0/observation_0/...`）交给适配器按输入字段格式化为用户消息内容，这样 LM 始终以统一结构读到完整历史。

提示：由于最后一次 LM 调用通常是 extract 阶段（抽取答案），`dspy.inspect_history(n=1)` 常见到的是抽取阶段的系统提示词（不含工具清单）。如果要查看包含工具清单的 react 阶段提示词，可用更早的一条记录（如 `inspect_history(n=2)`）或用回调截获 `messages`。

---

## 4. 运行机制（forward/aforward 循环）

核心逻辑位于 `ReAct.forward()`：

1) 初始化：`trajectory = {}`；解析 `max_iters`。
2) 迭代（最多 `max_iters` 次）：
   - 2.1 选择下一步：调用 `self.react(**input_args, trajectory=formatted_trajectory)` 得到
     - `next_thought: str`
     - `next_tool_name: one-of Literal`
     - `next_tool_args: dict`
     并把这些字段追加到 `trajectory`（键名按步号序号化）。
   - 2.2 执行工具：按 `next_tool_name` 查找 `self.tools[...]` 并调用：
     - 同步路径：`tool(**next_tool_args)`；
     - 异步路径：`await tool.acall(**next_tool_args)`；
     - 执行异常时将错误摘要写入 `observation_i`（不中断主循环）。
   - 2.3 结束判定：若 `next_tool_name == "finish"`，跳出循环。
3) 最终抽取（fallback）：调用 `self.extract(**input_args, trajectory=formatted_trajectory)`，在完整轨迹基础上生成基础签名定义的输出字段（如 `answer`）。
4) 返回：`dspy.Prediction(trajectory=trajectory, **extract)`。最终预测对象包含完整 `trajectory` 与任务输出；抽取阶段内部使用了 `ChainOfThought`（自动在签名前置一个推理字段以稳定输出），对外仍按基础输出字段访问。

异步版本 `aforward()` 同构，只是工具调用使用 `acall`，抽取阶段也用异步形式。

---

## 5. 工具（dspy.Tool）与参数校验

- 包装与推断：`adapters/types/tool.py` 的 `Tool` 会从函数签名与类型标注中自动推断：
  - `name`（默认函数名或可调用对象名）、`desc`（docstring）、`args`（JSON Schema）、`arg_types`（Python 类型）；
  - 例：`int/str/float/bool/list/object` 等，或 Pydantic BaseModel（递归展开 schema）。
- 运行与校验：
  - 同步调用 `__call__`、异步调用 `acall` 均带回调装饰（便于流式/监控）；
  - 运行前 `_validate_and_parse_args`：
    - 按 JSON Schema 校验字段与必选项，失败抛出 `ValueError`；
    - 再用 Pydantic 将值解析为期望的 Python 类型（含嵌套模型场景）。
  - `has_kwargs=True` 的工具允许多余参数透传；默认严格匹配。
- 内置 `finish` 工具：名称固定为 `finish`，无参，仅用于让模型显式结束循环。这一工具也一起进入工具清单与 `Literal` 限定集合。

---

## 6. 上下文窗口控制与轨迹截断

- 冲突来源：轨迹不断扩展可能导致总提示超出上下文窗口（LiteLLM 抛 `ContextWindowExceededError`）。
- 重试策略：`_call_with_potential_trajectory_truncation`/`_async_call_with_...` 最多重试 3 次；一旦捕获超窗错误，会调用 `truncate_trajectory()` 截掉“最早的一次工具调用”的 4 个键：`thought_i`、`tool_name_i`、`tool_args_i`、`observation_i`。
- 可定制：可覆写 `truncate_trajectory(self, trajectory)` 实现更精细策略（如优先保留最近 observation、对长文本 observation 先做摘要再保留）。
- 边界提示：当轨迹不足以删满“一整次工具调用”的 4 个键时会报错（默认实现下），从而提前结束循环。

---

## 7. 与 DSPy 生态的配合

- History 多轮对话：
  - 若基础签名含 `history: dspy.History` 输入字段，`ChatAdapter` 会自动把历史 `{user, assistant}` 对渲染进提示；ReAct 的 `trajectory` 只用于“当前轮/当前问题”内部的 Reason-Act 轨迹。
- Streaming 与回调：
  - `Tool.__call__/acall` 带回调；配合 `dspy.streaming.streamify` 与自定义 `StatusMessageProvider` 可在“工具前/后”输出用户可见的阶段性消息，适合单问题多步演示式交互。
- 提示/程序优化器：
  - ReAct 的两个子预测器可被优化器感知：`self.react = dspy.Predict(...)` 与 `self.extract = dspy.ChainOfThought(...)`；
  - 可用 `teleprompt` 家族（如 `MIPROv2`、`BootstrapFewShot`、`COPRO`、`SIMBA`、`GEPA`）优化“指令文案/示例轨迹/输出前缀”等，具体策略详见本仓库的相关文档。

---

## 8. 与“原生 Function Calling”的关系

- DSPy 支持“原生 function calling”路径（在签名中设置 `list[dspy.Tool]`、输出 `dspy.ToolCalls` 时，适配器会给 LiteLLM 填 `tools` 参数并回收 `tool_calls`；见 `adapters/base.py`）。
- ReAct 默认不走该模式，而是通过：
  - 在 instructions 中枚举工具清单；
  - 使用强类型输出（`Literal`/`dict`）约束 `next_tool_*` 字段；
  - 在 Python 侧自行执行工具与管理轨迹；
 以获得跨厂商的一致性与对“思考-行动-观测-抽取”循环的完全可控。

---

## 9. 常见问题与实践建议

- 工具选择/参数错误：
  - 若模型产出的 `next_tool_name` 不在 `Literal` 集合内或 `next_tool_args` 解析失败，会触发 `ValueError` 并提前结束；优先优化工具文案与基础签名的 instructions，减少歧义。
- 轨迹超窗：
  - 默认策略会删最早的一次调用信息；观察是否需要覆写 `truncate_trajectory` 保留“关键 observation”或为长文本做摘要。
- `inspect_history()` 看不到工具清单：
  - 通常是因为最后一条是抽取阶段；调大 `n` 或用回调截获 react 阶段的 `messages`。
- 工具函数的类型标注：
  - 直接决定参数 schema 与验证强度；务必提供准确的类型与简洁的描述。
- 行为规范写在基础签名的 `instructions`：
  - 例如“何时优先/避免某工具”“仅在信息充分时才调用 finish”“禁止臆造工具结果”“`next_tool_args` 必须是合法 JSON”等。

---

## 10. 最小用法示例

```python
import dspy

dspy.settings.configure(lm=dspy.LM("openai/gpt-4o-mini"))

# 1) 定义工具（带类型注解与简洁描述）
def get_weather(city: str) -> str:
    """Return weather for given city."""
    return f"Weather@{city}: sunny"

# 2) 定义基础签名（可加入领域说明）
sig = dspy.Signature(
    "question -> answer",
    instructions=(
        "先检索/调用工具再回答；仅在可直接产出答案或信息充分时结束。"
        "next_tool_args 必须是 JSON；不要编造工具名。"
    ),
)

# 3) 初始化 ReAct 并调用
react = dspy.ReAct(signature=sig, tools=[get_weather], max_iters=6)
pred = react(question="What's the weather in Tokyo today?")
print(pred.answer)
print(pred.trajectory)  # thought_i / tool_name_i / tool_args_i / observation_i ...
```

---

## 11. 关键实现摘录（便于溯源）

- ReAct 核心：`predict/react.py`
  - 指令拼装与工具清单注入（含内置 `finish`）；
  - `react_signature`/`fallback_signature` 构造；
  - forward/aforward 循环、轨迹维护与异常处理；
  - 上下文超限重试与 `truncate_trajectory` 默认策略。
- 适配器与消息：`adapters/base.py`、`adapters/chat_adapter.py`、`adapters/utils.py`
  - 系统提示词三部分（字段说明/结构/任务说明）；
  - `Literal`/复杂类型的提示与解析；
  - History 与 few-shot 的拼装；
  - 统一的 `[[ ## field ## ]] ... [[ ## completed ## ]]` 结构化格式。
- 工具与校验：`adapters/types/tool.py`
  - 函数到 Tool 的自动推断；
  - JSON Schema 校验 + Pydantic 解析；
  - 同步/异步调用与回调装饰。
- 子预测器：`predict/predict.py`（Predict）、`predict/chain_of_thought.py`（CoT）。

---

通过以上机制，dspy.ReAct 在不依赖特定厂商功能的前提下，提供了稳定、强类型、可优化、可观测的“思考-选工具-调用-抽取”统一执行框架；既适用于单问题多步的工具型 Agent，也能自然嵌入多轮对话与评测/优化流水线。

