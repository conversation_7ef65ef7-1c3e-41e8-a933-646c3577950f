# dspy MIPROv2 使用手册

## 设计思路（速览，无代码）

- 核心目标：自动为你的 DSPy 程序同时优化“自然语言指令”和“few-shot 示例”，在你定义的度量函数下最大化效果。
- 底层机制：
  - 启动阶段（Bootstrapping）：基于你的学生程序与少量标注数据，生成若干候选 few-shot 示例集（含随机打乱、只取标注、以及自举生成的多种变体）。
  - 提案阶段（Grounded Proposal）：读取程序代码与数据摘要，结合候选 few-shot 示例与提示技巧（tip），为每个模块生成多条“指令”候选。
  - 搜索阶段（Bayesian + Minibatch）：把“每个模块选哪条指令”和“选哪组 few-shot”视为离散搜索空间，使用 Optuna TPE 的贝叶斯优化在小批量（minibatch）上快速评估候选方案，并周期性在完整验证集上做全量评估，迭代逼近最佳组合。
- 需要准备：
  - 学生程序（任意 DSPy 模块/流水线）。
  - 训练样本与（可选）验证样本（支持仅输入或含标注；无验证集则会自动从训练数据中按约 20%/80% 切分）。
  - 度量函数（metric）：输入为 example 与 prediction，输出标量或布尔（建议归一到 [0,1]）。
  - 模型（可选）：可分别设置 prompt_model（用于生成指令）与 task_model（用于执行/评估）；缺省均为 dspy.settings.lm。
- 标准 SOP：
  1) 定义签名与学生程序；2) 准备训练/验证数据；3) 定义 metric；4) 选择运行强度 `auto={light|medium|heavy}`（或手动模式）；5) 以 `compile()` 启动优化；6) 保存并评估最佳程序；7) 需要时可再次叠加优化或做集成。

---

## 机制细节（源码要点）

### 1) Bootstrapping：few-shot 候选集
- 入口：`create_n_fewshot_demo_sets(...)`。
- 组成：
  - 零样例与仅标注 few-shot 版本（`LabeledFewShot`），以及通过 `BootstrapFewShot` 自举得到的 few-shot 版本；还会对数据做若干次随机打乱以产生多组候选。
  - 返回按模块分组的候选 few-shot 集合（每个模块对应若干套 demos）。
- 可控项：`max_bootstrapped_demos`、`max_labeled_demos`、`teacher/teacher_settings`、`metric_threshold` 等。

### 2) Grounded Proposal：指令候选生成
- 入口：`GroundedProposer(...).propose_instructions_for_program(...)`。
- 证据来源：
  - 数据摘要（dataset summary）。
  - 程序源码摘要（program-aware）。
  - 任务 few-shot 示例（fewshot-aware）。
  - 随机提示技巧 tip（例如“更具描述性/简洁/设定 persona”等）。
- 为每个模块生成 N 条指令，首条为原始指令，后续为提案生成；`init_temperature` 控制生成多样性。

### 3) 搜索：离散组合 + TPE + 小批量评估
- 搜索空间：
  - 对每个模块同时选择“指令 index”和（如允许）“few-shot 集 index”。
  - 使用 Optuna 的 `TPESampler(multivariate=True)` 做贝叶斯优化。
- 评估策略：
  - `minibatch=True` 时，在小批量上快速评估若干轮，并按设定步长周期性对完整验证集做全量评估，选取均值最佳的候选做全评；`minibatch=False` 则每轮都在全量验证集上评估。
  - 评估器：`dspy.evaluate.Evaluate(devset, metric, ...)`，度量按样本求和/平均后转百分比返回。
- 运行强度与默认值：
  - `auto` 预设（核心开箱即用体验）：
    - light：`n=6` 候选，`val_size=100`；
    - medium：`n=12`，`val_size=300`；
    - heavy：`n=18`，`val_size=1000`；
  - 在非 0-shot 情况下，指令候选通常取 `n/2`，few-shot 候选取 `n`，把更多预算用于 few-shot 搜索。
  - trial 数量近似：`max(2 * V * log2(n), 1.5 * n)`，其中 `V` 为变量数（每模块 1 或 2 个：指令+few-shot）。

### 4) 数据切分与约束
- 若未显式提供 `valset`，将把 `trainset` 按约 20%/80% 切分（最大验证集大小 1000）。
- `minibatch_size` 不能超过 `valset` 的大小（默认 35，可调）。

### 5) 主要配置与注意事项
- `MIPROv2(metric, auto=...)`：如设置 `auto`，则不得再显式传入 `num_candidates/num_trials`；若 `auto=None`，则必须手动提供 `num_candidates` 与 `num_trials`。
- `prompt_model/task_model`：分别用于指令生成与任务执行；默认共用 `dspy.settings.lm`。
- `teacher/teacher_settings`：可为自举阶段单独指定更强的教师与其 LM 配置。
- `num_threads/max_errors`：传给评估器并行度与错误上限；`provide_traceback` 可在评估时附带回溯。
- `log_dir/track_stats`：保存每轮候选程序 JSON 与轨迹、统计最优得分及候选列表。
- `requires_permission_to_run` 已移除：传入 True 会报错（兼容旧接口的提示）。

---

## 标准 SOP（无代码）

1) 设定 LM：必要时分别配置 `prompt_model` 与 `task_model`，否则直接用 `dspy.settings.configure(lm=...)`。
2) 定义签名与学生程序：按 DSPy 常规方式组织模块（Predict/CoT/ReAct/RAG 等均可）。
3) 准备数据：`trainset` 至少若干条（可带/不带标签）；如不提供 `valset`，会自动切分。
4) 定义度量函数 `metric(example, prediction) -> float|bool`，建议返回 [0,1]。
5) 选择运行强度：优先用 `auto="light|medium|heavy"` 开箱即用；或设 `auto=None` 手动给 `num_candidates/num_trials`。
6) 运行优化：调用 `compile(student, trainset=..., valset=可选, max_bootstrapped_demos, max_labeled_demos, ...)`。
7) 保存与评估：对 `best_program` 保存 JSON；使用 `dspy.Evaluate` 做正式评估或上线。
8) 复用与叠加：可把最优程序再次作为 `teacher` 或继续用 MIPROv2/Ensemble/GEPA/SIMBA 做下一步优化。

---

## 最小 Demo（可直接替换到你的工程中）

```python
import dspy

# 1) 配置 LM（示例使用 OpenAI；需事先配置好 API Key）
lm = dspy.LM("openai/gpt-4o-mini")
dspy.settings.configure(lm=lm)

# 2) 定义签名与学生程序
class QA(dspy.Signature):
    """回答事实性问题，输出尽量简短。"""
    question: str = dspy.InputField()
    answer: str = dspy.OutputField()

class QAProgram(dspy.Module):
    def __init__(self):
        super().__init__()
        self.solve = dspy.Predict(QA)  # 可改为 ChainOfThought 等

    def forward(self, question: str):
        return self.solve(question=question)

# 3) 构造少量训练/验证数据（无显式 valset 时自动切分）
trainset = [
    dspy.Example(question="What is the capital of France?", answer="Paris").with_inputs("question"),
    dspy.Example(question="What is the capital of Japan?", answer="Tokyo").with_inputs("question"),
    dspy.Example(question="What is the capital of Italy?", answer="Rome").with_inputs("question"),
    dspy.Example(question="What is the capital of Germany?", answer="Berlin").with_inputs("question"),
    dspy.Example(question="What is the capital of Spain?", answer="Madrid").with_inputs("question"),
    dspy.Example(question="What is the capital of Canada?", answer="Ottawa").with_inputs("question"),
    dspy.Example(question="What is the capital of China?", answer="Beijing").with_inputs("question"),
    dspy.Example(question="What is the capital of Brazil?", answer="Brasília").with_inputs("question"),
    dspy.Example(question="What is the capital of Australia?", answer="Canberra").with_inputs("question"),
    dspy.Example(question="What is the capital of Mexico?", answer="Mexico City").with_inputs("question"),
]

# 4) 定义度量函数（精确匹配）
def exact_match(example, pred, trace=None):
    gold = str(example.answer).strip().lower()
    got = str(getattr(pred, "answer", "")).strip().lower()
    return 1.0 if gold == got else 0.0

# 5) 初始化并运行 MIPROv2（开箱即用：auto="light"）
tp = dspy.MIPROv2(
    metric=exact_match,
    auto="light",            # 可改为 "medium" / "heavy"；或设置 auto=None 手动给 num_candidates/num_trials
    num_threads=8,            # 并行评估线程数（按你的限流与机能调整）
)

optimized = tp.compile(
    student=QAProgram(),
    trainset=trainset,
    max_bootstrapped_demos=3,
    max_labeled_demos=3,
)

# 6) 评估与使用
eval_program = dspy.Evaluate(devset=trainset, metric=exact_match, display_progress=True)
result = eval_program(optimized)
print("MIPROv2 优化后得分:", result.score)

print(optimized(question="What is the capital of France?").answer)
```

提示：
- 数据很小时会自动退化为全量评估（非 minibatch）；确保 `minibatch_size <= len(valset)`。
- 若要只优化“指令而保持 0-shot”，把 `max_bootstrapped_demos=0` 与 `max_labeled_demos=0` 设为 0。
- 如需在自举阶段使用更强/不同的教师，可传入 `teacher=` 与 `teacher_settings={"lm": stronger_lm}`。
