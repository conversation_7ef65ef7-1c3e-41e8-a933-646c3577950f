**目标**
- 基于本仓库 dspy 的实现，给出“如何用 dspy 处理历史对话并实现一个最小可用的多轮 Chatbot”的方法与示例代码。

**核心结论**
- 历史对话用 `dspy.History` 表达，并作为签名的一个输入字段传入；其内部是一个消息列表，每条消息的键名必须与签名中的字段名匹配。
- dspy 的默认对话格式化由 `ChatAdapter` 完成，它会把“系统提示 + few-shot 示例 + 历史消息 + 当前输入”拼成多轮消息并送入底层 LM。
- 最小实现思路：定义一个带 `history: dspy.History` 的签名，用 `dspy.Predict` 推理；每轮结束把本轮的输入/输出附加到 `History`，下一轮再传回去。

**源码定位（便于对照理解）**
- `adapters/types/history.py:6` 定义了 `dspy.History`，说明了历史消息的结构与用法。
- `adapters/base.py:156` 的 `Adapter.format(...)` 描述了如何把系统消息、few-shot、历史与当前输入组合成多轮 `messages`；`adapters/base.py:412` 的 `format_conversation_history(...)` 具体将 `History` 展开为交替的 user/assistant 消息。
- `adapters/chat_adapter.py:28` 是默认的 `ChatAdapter`，含字段结构与解析规则，保证输出能被严格解析为签名的输出字段。
- `predict/predict.py:118` 和 `predict/predict.py:217` 显示 `dspy.Predict` 默认使用 `ChatAdapter` 来格式化对话并完成推理。

**最小多轮 Chatbot 示例**
```python
import dspy

# 1) 配置底层模型（示例为 OpenAI；你也可以换成你本地/其他后端）
#    常见做法是用环境变量提供 API Key，比如：OPENAI_API_KEY
dspy.settings.configure(lm=dspy.LM("openai/gpt-4o-mini"))

# 2) 定义签名：包含用户输入、对话历史、以及模型回答
class ChatSignature(dspy.Signature):
    """
    你是一个乐于助人的聊天助手。回答要简洁、事实准确，必要时可追问澄清。
    """
    question: str = dspy.InputField(desc="用户当前的问题或消息")
    history: dspy.History = dspy.InputField(desc="历史对话，按需传入，可为空")
    answer: str = dspy.OutputField(desc="助手机器人的回答")

# 3) 构造预测器
chat = dspy.Predict(ChatSignature)

# 4) 会话循环：维护 History，并在每轮调用时传回
history = dspy.History(messages=[])

print("开始聊天（输入 /exit 退出）")
while True:
    q = input("你: ").strip()
    if q in {"/exit", "exit", "quit"}:
        break
    if not q:
        continue

    # 传入历史与当前问题
    o ory=history)
    print("Bot:", out.answer)

    # History 是不可变(pydantic frozen)，需构造新对象
    history = dspy.History(
        messages=history.messages + [{"question": q, "answer": out.answer}]
    )
```

**关键点说明**
- 历史消息的字段名要与签名匹配：上面的签名是 `question -> answer`，因此 `History.messages` 里每条都应是 `{"question": ..., "answer": ...}`。
- `History` 可省略：首次轮次可以不传或传空的 `History`，`ChatAdapter` 会只用“系统提示 + 当前输入”。
- 不可变对象：`dspy.History` 在 `adapters/types/history.py:61` 设置为 `frozen=True`，因此更新历史要创建新对象（如示例所示）。
- 系统提示与输出结构：`ChatAdapter` 会把签名字段、输入/输出结构与指令合并为系统消息，并强制模型按 `[[ ## field_name ## ]]` 段落返回；详见 `adapters/chat_adapter.py:75`、`adapters/chat_adapter.py:169`。

**为什么这样就能“多轮”**
- `Adapter.format(...)` 会：
  - 生成系统消息（含字段描述/结构/任务目标）；
  - 追加 few-shot 示例（若有）；
  - 展开 `History`：把每条历史转成 user/assistant 两条消息；
  - 放入“本轮用户输入”。
  这让底层 LM 在一个请求中感知到“上下文 + 当前轮输入”，实现多轮衔接；见 `adapters/base.py:156` 与 `adapters/base.py:412`。

**常见变体**
- 自定义系统提示：
  - 直接使用类 docstring（如上）或在类定义后赋值 `ChatSignature.instructions = "..."`，`ChatAdapter` 会把该指令编入系统消息。
- 加 Few-shot：
  - `chat = dspy.Predict(ChatSignature)` 后，可设置 `chat.demos = [{"question": "...", "answer": "..."}, ...]` 用作演示示例。
- 控制生成参数：
  - 在调用时传 `config={"temperature": 0.2}` 等：`chat(question=..., history=..., config={...})`。
- 精简/裁剪历史：
  - 如果出现上下文过长，可只保留最近若干轮或做摘要；本质就是裁剪 `history.messages` 再传入。

**故障与排查**
- 字段不匹配导致解析失败：`ChatAdapter` 要求模型输出能被解析为签名中的全部输出字段；若字段缺失或类型不符，会触发解析错误（见 `adapters/chat_adapter.py:169`）。检查签名字段、历史条目键名、和模型输出内容。
- 未配置 LM：若没 `dspy.settings.configure(lm=...)`，`dspy.Predict` 会报错提示先配置模型；见 `predict/predict.py:118`。

**更进一步（可选）**
- 封装成有状态的模块：
```python
class Chatbot(dspy.Module):
    def __init__(self):
        super().__init__()
        self.predict = dspy.Predict(ChatSignature)
        self.history = dspy.History(messages=[])

    def __call__(self, question: str) -> str:
        out = self.predict(question=question, history=self.history)
        self.history = dspy.History(messages=self.history.messages + [
            {"question": question, "answer": out.answer}
        ])
        return out.answer

bot = Chatbot()
print(bot("你好"))
print(bot("再解释一下上一句的意思？"))
```

这样可以把“历史维护”的细节封装在模块内部，调用侧只管传 `question`。

**小结**
- 用 dspy 做多轮对话的关键是：在签名中加入 `dspy.History` 输入字段，并在每轮调用后把本轮输入/输出追加回 `History` 传给下一轮。
- `ChatAdapter` 会负责将系统提示、few-shot、历史与当前输入组织为标准的 chat messages，并严格解析输出，保证稳定的结构化返回。
