**面向 dspy.ReAct 的提示词优化指南**

- 适用对象: 已用 `dspy.ReAct` 构建的工具调用式预测器（agent）。
- 目标: 提高工具选择正确率、减少无效回合、提升最终输出质量与一致性。

**关键位置与原理**

- 基础签名说明注入: `ReAct` 会把你在基础签名上的 `instructions` 作为整段最前置说明插入，随后拼接 ReAct 通用指引与工具清单。因此应把领域约束与行为规范写在基础签名的 `instructions` 中，而不是修改源码。
  - 参考: `predict/react.py:50` 将基础 `signature.instructions` 注入。
  - 参考: `predict/react.py:52` 起拼接通用 ReAct 指南（如何交替输出 thought/tool/args、可见历史 trajectory 等）。
- 工具文档即提示词: ReAct 会把每个 `Tool` 的名称、描述、参数模式逐条渲染进提示词，质量直接决定工具选取的可控性。
  - 参考: `predict/react.py:70` 按序枚举工具进入提示词；`predict/react.py:63` 注入内置 `finish` 工具。
- 严格 JSON 的参数约束: 提示里明确要求 `next_tool_args` 必须是 JSON 格式，有助于降低解析错误。
  - 参考: `predict/react.py:72` 明确 JSON 约束。
- ReAct 的两个内部预测器都可被优化器“看见”: 用于选工具的 `self.react = dspy.Predict(...)` 与最终抽取答案的 `self.extract = dspy.ChainOfThought(...)`。
  - 参考: `predict/react.py:88`、`predict/react.py:89`。
- 长上下文控制: 超限会自动截断最早一次工具调用的四个键（thought/name/args/observation）。
  - 参考: `predict/react.py:146` 起的截断调用；`predict/react.py:168` 可覆写 `truncate_trajectory` 自定义保留策略。

**先做这些提示词内优化（无代码/轻代码）**

- 明确行为规范（写入基础签名 instructions）
  - 工具策略: 何时首选/禁用某工具、避免循环调用、解析工具输出后再行动、仅在“信息充分”时调用 `finish`。
  - 输出约束: 只产出目标字段，不要额外解释；必要时为最终输出字段设置固定前缀（便于评测/解析）。
  - 错误兜底: 工具失败要改用备选工具/重试并简述原因；不可凭空捏造工具结果。
  - 思维格式: `next_thought` 中先列计划再给出理由，短句为主，避免复述历史。
- 优化 Tool 定义（等于优化提示中的“工具清单”）
  - 名称: 动词+对象，唯一、可区分；避免泛化名如 `run`/`tool1`。
  - 描述: 说清输入输出、适用边界、失败形态；写明“何时不用它”。
  - 参数: 类型+取值范围+示例；不确定项用可选字段，鼓励默认值；避免包含冗长文本。
  - 顺序: 常用、精确的工具排在靠前（模型有位置偏置）。

**推荐优化器与各自优化 TODO**

- MIPROv2（首选，端到端搜索指令+少样例）
  - 适用: 需要同时优化“指令文案”和“示例轨迹（few-shot demos）”，且有评测度量 `metric`。
  - TODO:
    - 定义任务度量 `metric(example, prediction, trace)`，能打分或返回 bool。
    - 准备 `trainset` 与可选 `valset`；无 `valset` 时让 MIPROv2 自动切分。
    - 初始化: `dspy.teleprompt.MIPROv2(metric, auto="light|medium|heavy", max_bootstrapped_demos, ...)`。
    - 编译: `best = MIPROv2.compile(student=react_program, trainset=trainset, teacher=teacher?)`。
    - 根据预算调参: `auto` 决定候选规模；必要时提高 `max_bootstrapped_demos`、打开 `fewshot_aware_proposer`。
    - 验证: 用 `Evaluate` 在 held-out 上复核；保留 `best_program`。

- BootstrapFewShot（快速稳定的 few-shot 轨迹注入）
  - 适用: 先把“好轨迹”蒸馏进提示；无须大规模搜索。
  - TODO:
    - 定义 `metric`（可选设 `metric_threshold` 过滤轨迹）。
    - 以当前程序作为 teacher 运行若干训练样本，自动收集成功轨迹并作为 demos 写回每个预测器。
    - 关键参数: `max_bootstrapped_demos`（每预测器 demo 上限）、`max_rounds`（重采样以绕开缓存）。

- BootstrapFewShotWithRandomSearch（快速估计最优 demo 数/采样种子）
  - 适用: 小预算场景下“demo 数量/顺序很敏感”，想粗粒度找峰值。
  - TODO: 设 `num_candidate_programs`，让其对不同 demo 数/顺序采样并在 `valset` 上评测，挑分数最高的程序。

- COPRO（只改“指令+输出前缀”的文案进化）
  - 适用: 已有 demo，但“指令措辞”仍可进化；或更想控制最终输出字段的开头前缀（例如统一以“Final answer:”起始）。
  - TODO:
    - 定义 `metric`，准备 `trainset`。
    - `teleprompt.COPRO(prompt_model=?, metric=?, breadth, depth)` 逐层生成多个指令候选并打分选优。
    - 注意: ReAct 的 `react` 预测器最后字段是 `next_tool_args`（结构化），前缀优化主要作用于 `extract`（最终输出）。

- SIMBA（自反式规则+demo 的小批梯度上升）
  - 适用: 需要系统性降低“工具误用/幻觉/无效回合”，将失败转化为“可执行的规则”附加到指令中。
  - TODO:
    - 设定 `metric`、`bsize`、`max_steps`、`max_demos`。
    - 运行后会自动：采样差异大的样本 → 生成规则/增添 demos → 评测 → 迭代。最终把规则追加到每个预测器的 `instructions`。

- GEPA（反思式进化，支持按预测器粒度的文本反馈）
  - 适用: 你能从 `trace`/单个预测器层面给出文本化反馈时，做“有指导的指令进化”。
  - TODO:
    - 提供能返回 `float` 或 `ScoreWithFeedback` 的 `metric`（可含 predictor 级反馈）。
    - `gepa = GEPA(metric=..., auto="light|...", track_stats=...)`，`compile(student, trainset, valset=...)`。
    - 备注: 需要 `gepa` 依赖；为实验性质，适合集中攻关 prompt 文案质量。

- KNNFewShot（推理时按相似度检索 demo，再做 few-shot 编译）
  - 适用: 任务分布多样、局部模式强；希望“就近取样”的 ReAct 轨迹提升工具策略。
  - TODO: 准备 `Embedder`（如 `SentenceTransformer`），初始化 `KNNFewShot(k, trainset, vectorizer)`，把它包在你的 ReAct 程序外层再调用。

- LabeledFewShot（纯手工/标签示例）
  - 适用: 你已有极少但高质量的人工 ReAct 轨迹，直接塞进提示作为稳态先验。
  - TODO: 构造包含 `inputs + trajectory -> next_*` 与最终输出的示例，使用 `LabeledFewShot(k=...)` 编译。

- BetterTogether（提示+权重交替优化的组合策略）
  - 适用: 在 prompt 优化基础上继续蒸馏到权重（如 `BootstrapFinetune`），或两者交替。
  - TODO: 需要更多算力与稳定评测，优先在 prompt 侧收敛后再启用。

**ReAct 特有的提示细化建议**

- 指令里显式强调：
  - `next_tool_args` 必须为合法 JSON；字段名与工具签名一致，禁止多余字段。
  - `next_tool_name` 必须从清单中精确拣选；无法决定时先产出思考再选择，不要随意编造工具名。
  - 只有在“已具备生成输出所需全部关键信息”时才调用 `finish`；否则继续检索/计算。
  - 思考要短、面向下一步，不复述轨迹历史（节省上下文）。
- 为最终输出字段设置前缀（只影响 `extract` 阶段）
  - 使用 `.with_updated_fields(last_key, prefix="Final answer: ")` 固定输出开头，利于打分与解析。
- 大上下文任务可自定义截断策略
  - 继承 `ReAct` 并覆写 `truncate_trajectory`，优先保留最近一次的 observation 与关键工具的结果，或把冗长 observation 先行摘要。

**推荐落地组合（按预算）**

- 低预算/快速稳定: BootstrapFewShot → COPRO（只修文案）
- 中预算/多数场景: MIPROv2(auto="light")（一站式指令+few-shot）
- 高预算/追求极致: MIPROv2(auto="heavy") → SIMBA（收敛难点处追加规则）/GEPA（带反馈的定向进化）
- 分布多样/在线检索: KNNFewShot 包装 + BootstrapFewShot 少样例蒸馏

**最小化代码骨架（示例）**

```python
# 1) 定义 metric
def metric(ex, pred, trace=None):
    # ex: dspy.Example, pred: dspy.Prediction
    # 返回 bool 或 [0,1] 分数
    return int(pred.answer.strip() == ex.answer.strip())

# 2) 构造 ReAct 程序（把领域规范写在基础签名的 instructions）
class QA(dspy.Module):
    def __init__(self, tools):
        super().__init__()
        sig = dspy.Signature(
            "question -> answer",
            instructions=(
                "先检索再回答；严禁凭空编造。必要时调用多个工具，"
                "只有在信息充足时才 finish；最终答案只输出 answer 字段。"
            ),
        )
        self.agent = dspy.ReAct(sig, tools=tools, max_iters=6)
    def forward(self, question):
        return self.agent(question=question)

# 3) 一键优化（MIPROv2）
opt = dspy.teleprompt.MIPROv2(metric=metric, auto="light", max_bootstrapped_demos=4)
best_program = opt.compile(student=QA(tools=[...]), trainset=trainset)
```

**排错与常见坑**

- 工具文案过长/含歧义: 精简、强调适用边界；把示例放到参数 doc 而非长段说明。
- 轨迹超限导致截断: 减少无用思考/观测文本；必要时覆写 `truncate_trajectory` 或在 Tool 内部先做摘要。
- COPRO 作用范围: 它主要改“指令+最终输出前缀”，对 `react` 的 `next_tool_args`（结构化）不做前缀优化。
- 评测函数不稳定: metric 应稳定、单调，避免把“偶然的好答案”当最好提示。

以上策略均与当前仓库实现保持一致，可直接套用；若需要，我可以继续为你的具体 ReAct 用例起草 instructions 初稿和 Tool 文案模板。
