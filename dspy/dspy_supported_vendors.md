# dspy 支持的大模型供应商

DSPy 的 `dspy.LM` 统一通过 LiteLLM 调用模型，因此在推理阶段可以直接接入 LiteLLM 支持的各类供应商。只需按照每家厂商的环境变量规范，传入 `{provider}/{model}` 的模型名即可完成切换。

## LiteLLM 直接兼容的主流厂商
- OpenAI：通过 `openai/` 前缀配置 GPT-4o 等模型，支持以 `OPENAI_API_KEY` 鉴权。
- Google Gemini（AI Studio）：使用 `gemini/` 前缀并提供 `GEMINI_API_KEY`。
- Anthropic：通过 `anthropic/` 前缀和 `ANTHROPIC_API_KEY` 接入 Claude 3 系列。
- Databricks：在 Databricks 环境或通过 `DATABRICKS_API_KEY`、`DATABRICKS_API_BASE` 直接调用其托管的 Llama3 等模型。
- 其它 LiteLLM 列表中的供应商：包括 AnyScale、Together、AWS SageMaker、自建 Azure OpenAI 部署等，只需按照文档设置对应的 API Key 和 Base URL。

## 本地部署的常见路径
- GPU 服务器上的 SGLang：先通过 SGLang 部署模型，再以 OpenAI 兼容的 `openai/<model>` 形式连接。
- 本地机器上的 Ollama：通过 `ollama_chat/<model>` 前缀与本地 Ollama 进程交互。

## DSPy 内置的 Provider 扩展
在需要微调、强化学习或托管管理时，DSPy 还内置了专用 Provider 模块：
- `OpenAIProvider`：封装 OpenAI 微调作业的提交、状态查询与模型检索流程。
- `DatabricksProvider`：支持将训练数据上传到 Databricks 并调用其 Foundation Model 微调与部署能力。
- `LocalProvider`：面向自建的 SGLang 推理服务，提供本地模型的启动、停止与 SFT 微调封装。
- `ArborProvider`：集成 Arbor 平台，涵盖训练、强化学习和模型部署的生命周期管理。

以上供应商之外，只要 LiteLLM 提供连接器（详见其文档中的 Providers 列表），都可以通过 `dspy.LM` 直接接入。
