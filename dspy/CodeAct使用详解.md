# CodeAct 使用详解

本笔记结合源码与官方文档，整理 DSPy 中 `CodeAct` 预测器的使用方式、执行流程与实践要点。

## 模块定位
- `CodeAct` 同时继承 ReAct 与 ProgramOfThought，整合工具调用与代码执行能力（`predict/code_act.py:14`）。
- 适合需要“边思考边编写 Python 代码”并可借助预置工具函数的复杂任务，例如数学推理、数据处理或需要中间结果验证的场景。

## 关键组成
- 构造函数要求任务签名、工具列表、最大迭代次数与可选解释器实例（`predict/code_act.py:19-69`）。
- 工具在初始化时会被包装成 `Tool` 对象，并被放入字典供提示词列举（`predict/code_act.py:44-66`，`adapters/types/tool.py:13-180`）。
- 模块内部包含两个子预测器：
  - `self.codeact`：负责生成下一段待执行的 Python 代码以及 `finished` 标记。
  - `self.extractor`：在完成后读取轨迹并产出最终输出字段。
- Python 代码由 `PythonInterpreter` 运行，默认使用 Deno + Pyodide 沙箱（`primitives/python_interpreter.py:13-218`）。

## 签名与提示词结构
- `CodeAct` 会基于原始签名拼装一段多轮指令，明确要求模型：
  - 读取输入字段、理解目标输出。
  - 在每次迭代输出围栏包裹的 Python 代码片段。
  - 在可得出最终答案时将 `finished=True`（`predict/code_act.py:71-88`）。
- 最终传给 `self.codeact` 的签名含有输入字段、`trajectory`、`generated_code` 与 `finished`，保证模型能访问历史轨迹并返回代码字符串（`predict/code_act.py:53-57`）。
- 抽取阶段沿用原任务签名，加上 `trajectory` 输入交给 `ChainOfThought` 总结（`predict/code_act.py:60-67`）。

## 执行流程
1. **注入工具**：每次调用 `forward` 时，先把工具函数源码写入解释器环境，因此工具必须是常规函数且 `inspect.getsource` 可拿到源码（`predict/code_act.py:90-94`）。
2. **循环生成代码**：
   - 调用 `self.codeact` 获取本轮代码与 `finished`（`predict/code_act.py:97-115`）。
   - 通过 ProgramOfThought 的 `_parse_code` 抽取 fenced block 代码文本（`predict/program_of_thought.py:135-158`）。
   - 把成功解析的代码记录到轨迹，并交给解释器执行；输出或错误也落到轨迹里（`predict/code_act.py:106-112`）。
3. **停止条件**：若模型返回 `finished=True` 或迭代次数达到上限即跳出循环（`predict/code_act.py:114-115`）。
4. **答案抽取**：利用 ReAct 中的 `_call_with_potential_trajectory_truncation` 将轨迹喂给 `self.extractor`，得到最终输出字段，并关闭解释器（`predict/code_act.py:117-118` 与 `predict/react.py:90-146`）。
5. **返回结构**：`forward` 返回 `dspy.Prediction`，包含原签名输出与完整 `trajectory`，方便调试。

## 工具注入与约束
- 仅支持函数，而非类实例或 `functools.partial` 等可调用对象，违规则抛错（`predict/code_act.py:44-48`）。
- 工具函数需具备类型注解，以便 `Tool` 自动生成参数 schema，供模型填写 JSON 形态的入参（`adapters/types/tool.py:53-120`）。
- 工具可以是同步或异步函数；异步需通过 `Tool.acall`，但 `CodeAct` 目前同步执行工具函数源码，因此通常使用同步实现。

## PythonInterpreter 与环境要求
- 解释器基于 Deno 调用 Pyodide，需要本机已安装 `deno` 并允许读/写对应路径（`primitives/python_interpreter.py:28-214`）。
- 解释器会在每次调用结束时 `shutdown`，避免资源泄露（`predict/code_act.py:118`）。
- 若需要自定义权限（读写路径、环境变量、网络访问），可以手动实例化 `PythonInterpreter` 传入 `CodeAct` 构造函数。

## 使用示例
```python
import dspy

# 1. 配置语言模型
lm = dspy.LM("openai/gpt-4o-mini")
dspy.configure(lm=lm)

# 2. 定义工具函数（必须能被 inspect.getsource 捕获）
def get_city_temp(city: str) -> float:
    """示例工具：返回硬编码的城市温度。"""
    temps = {"北京": 23.5, "上海": 24.2, "深圳": 27.8}
    return temps.get(city, 25.0)

# 3. 初始化 CodeAct
act = dspy.CodeAct(
    signature="city -> answer",
    tools=[get_city_temp],
    max_iters=4,
)

# 4. 调用并查看结果
result = act(city="北京")
print(result.answer)        # 最终答案
print(result.trajectory)    # 每轮生成的代码与输出
```

### 自定义高阶签名
- 可以传入继承自 `dspy.Signature` 的类，定义多输入/多输出任务；`CodeAct` 会自动拼装指令并在抽取阶段复用原字段（`predict/code_act.py:53-64`）。
- 当输出字段包含结构化信息时，可让 `self.extractor` 的 `ChainOfThought` 负责整理，使最终返回符合签名约束。

## 轨迹与调试
- 轨迹键名示例：`generated_code_0`、`code_output_0`、`observation_0` 等，便于观察失败原因或中间结果（`predict/code_act.py:101-112`）。
- 若代码解析或执行失败，对应错误信息会写入 `observation_i` 字段；后续迭代可以据此改进代码。
- `self.extractor` 使用 ReAct 的轨迹截断逻辑，防止上下文超限，必要时可自定义 `truncate_trajectory`（`predict/react.py:107-151`）。

## 与其他预测器的关系
- 与 **ReAct**：继承其轨迹管理与工具提示结构，但行动不再是“调用工具”，而是“生成代码 + optional 工具调用”并执行。
- 与 **ProgramOfThought**：复用其代码解析与执行逻辑，实现“多轮代码重试”，但 `CodeAct` 只保留 `generate` 搭配 `finished` 标记的流程；不做 `regenerate` 回退，因此 `max_iters` 主要限制循环次数。
- 官方总结文档亦强调了“代码 + 工具”混合策略与 Deno 依赖，可作为快速参考（`dspy_Agnt预测器.md:64-108`）。

## 实践建议
1. **提前校验工具**：确保函数为顶层定义且能成功 `inspect.getsource`，否则在第一次调用时就会失败。
2. **合理设置 max_iters**：复杂问题可适当调高，避免模型尚未完成即被截断；但过大可能导致冗余迭代。
3. **观察 trajectory**：定位错误最直接的方式，必要时可以把失败代码复制到本地运行或单步调试。
4. **管理沙箱权限**：若代码需访问文件或网络，可手动构造 `PythonInterpreter` 并显式允许路径/域名。
5. **结合评估器**：在需要稳定性的场景，可配合 DSPy 的评估/重试框架，对 `CodeAct` 的输出做检测后再采纳。
